{"manifest_version": 3, "name": "StickyLink", "version": "1.0.0", "description": "A hierarchical todo and notes extension with URL-based organization and sticky linking", "permissions": ["storage", "activeTab", "tabs", "scripting", "sidePanel"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "css": ["content.css"]}], "action": {"default_title": "StickyLink", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "side_panel": {"default_path": "popup.html"}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["popup.html", "popup.css", "popup.js", "fullscreen.html"], "matches": ["<all_urls>"]}]}