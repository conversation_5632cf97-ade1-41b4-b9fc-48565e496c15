// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'all'; // Default to all view
        this.currentGrouping = 'none';
        this.globalMode = false; // Keep for backward compatibility, but will be phased out
        this.currentEditingTodoId = null;

        // Per-item global mode states
        this.todoGlobalMode = false;
        this.noteGlobalMode = false;
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        this.settings = {
            defaultMode: 'sidebar', // Default to sidebar mode
            showCompleted: true,
            fullscreenLayout: 'vertical',
            closePreviousMode: true
        };

        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();
        await this.loadTheme();

        // Skip sidebar mode check in fullscreen
        if (!document.body.classList.contains('fullscreen')) {
            this.checkSidebarMode();
        } else {
            // In fullscreen mode, default to global mode
            this.globalMode = true;
            this.updateGlobalModeDisplay();
        }

        this.updateUI();

        // Ensure counts are updated after everything is loaded (skip in fullscreen)
        if (!document.body.classList.contains('fullscreen')) {
            setTimeout(() => {
                this.updateCounts();
            }, 100);
        }
    }
    
    async loadCurrentTab() {
        try {
            // Skip tab loading in fullscreen mode
            if (document.body.classList.contains('fullscreen')) {
                this.currentTab = null;
                return;
            }

            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });

            if (this.currentTab) {
                const currentUrlEl = document.getElementById('current-url');
                if (currentUrlEl) {
                    currentUrlEl.textContent = this.formatUrl(this.currentTab.url);
                }
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache', 'settings'], resolve);
            });

            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
            this.settings = { ...this.settings, ...(result.settings || {}) };
            console.log('Loaded settings:', this.settings, 'from storage:', result.settings);
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Scope selection
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchScope(e.target.dataset.scope);
            });
        });

        // Grouping selection
        const groupByEl = document.getElementById('group-by');
        if (groupByEl) {
            groupByEl.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;
                this.updateUI();
            });
        }

        // New item buttons
        const newTodoBtnEl = document.getElementById('new-todo-btn');
        if (newTodoBtnEl) {
            newTodoBtnEl.addEventListener('click', () => {
                this.showAddTodo();
            });
        }

        const newNoteBtnEl = document.getElementById('new-note-btn');
        if (newNoteBtnEl) {
            newNoteBtnEl.addEventListener('click', () => {
                this.showAddNote();
            });
        }

        // Add todo - auto-save and improved handling

        // Simple todo input handling
        const todoInputEl = document.getElementById('todo-input');
        if (todoInputEl) {
            // Handle Enter key - create todo immediately
            todoInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const text = todoInputEl.value.trim();
                    if (text) {
                        this.addTodo();
                    } else {
                        this.hideAddTodo();
                    }
                }
            });

            // Handle ESC key - delete if empty, otherwise close
            todoInputEl.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.handleTodoEscape();
                }
            });

            // Auto-save on blur (when clicking outside)
            todoInputEl.addEventListener('blur', (e) => {
                // Small delay to allow other click events to process first
                setTimeout(() => {
                    const text = todoInputEl.value.trim();
                    if (text) {
                        this.addTodo();
                    } else {
                        this.hideAddTodo();
                    }
                }, 100);
            });

            // Auto-save on input change (after a delay)
            let autoSaveTimeout;
            todoInputEl.addEventListener('input', (e) => {
                clearTimeout(autoSaveTimeout);
                const text = e.target.value.trim();
                if (text) {
                    autoSaveTimeout = setTimeout(() => {
                        this.addTodo();
                    }, 2000); // Auto-save after 2 seconds of no typing
                }
            });
        }

        const cancelTodoBtnEl = document.getElementById('cancel-todo-btn');
        if (cancelTodoBtnEl) {
            cancelTodoBtnEl.addEventListener('click', () => {
                this.hideAddTodo();
            });
        }

        // Calendar button (only exists in popup mode)
        const calendarBtnEl = document.getElementById('calendar-btn');
        if (calendarBtnEl) {
            calendarBtnEl.addEventListener('click', () => {
                this.showDatePicker();
            });
        }

        // Add note
        const addNoteBtnEl = document.getElementById('add-note-btn');
        if (addNoteBtnEl) {
            addNoteBtnEl.addEventListener('click', () => {
                this.addNote();
            });
        }

        // Simple note input handling
        const noteTitleEl = document.getElementById('note-title');
        const noteContentEl = document.getElementById('note-content');
        if (noteTitleEl && noteContentEl) {
            // Handle Ctrl+Enter key in content area - create note immediately
            noteContentEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    const title = noteTitleEl.value.trim();
                    const content = noteContentEl.value.trim();
                    if (content) {
                        this.addNote();
                    } else {
                        this.hideAddNote();
                    }
                }
            });

            // Handle ESC key - delete if empty, otherwise close
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    this.handleNoteEscape();
                }
            };

            noteTitleEl.addEventListener('keydown', handleEscape);
            noteContentEl.addEventListener('keydown', handleEscape);

            // Auto-save on blur (when clicking outside)
            const handleBlur = (e) => {
                setTimeout(() => {
                    const title = noteTitleEl.value.trim();
                    const content = noteContentEl.value.trim();
                    if (content) {
                        this.addNote();
                    } else {
                        this.hideAddNote();
                    }
                }, 100);
            };

            noteTitleEl.addEventListener('blur', handleBlur);
            noteContentEl.addEventListener('blur', handleBlur);

            // Auto-save on input change (after a delay)
            let autoSaveTimeout;
            const handleInput = (e) => {
                clearTimeout(autoSaveTimeout);
                const content = noteContentEl.value.trim();
                if (content) {
                    autoSaveTimeout = setTimeout(() => {
                        this.addNote();
                    }, 3000); // Auto-save after 3 seconds for notes (longer than todos)
                }
            };

            noteTitleEl.addEventListener('input', handleInput);
            noteContentEl.addEventListener('input', handleInput);
        }

        const cancelNoteBtnEl = document.getElementById('cancel-note-btn');
        if (cancelNoteBtnEl) {
            cancelNoteBtnEl.addEventListener('click', () => {
                this.hideAddNote();
            });
        }

        // Settings
        const settingsBtnEl = document.getElementById('settings-btn');
        if (settingsBtnEl) {
            settingsBtnEl.addEventListener('click', () => {
                this.openSettings();
            });
        }

        const closeSettingsEl = document.getElementById('close-settings');
        if (closeSettingsEl) {
            closeSettingsEl.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        // Theme selector
        const themeSelectEl = document.getElementById('theme-select');
        if (themeSelectEl) {
            themeSelectEl.addEventListener('change', (e) => {
                this.setTheme(e.target.value);
            });
        }

        // Toggle completed todos
        const toggleCompletedEl = document.getElementById('toggle-completed');
        if (toggleCompletedEl) {
            toggleCompletedEl.addEventListener('click', (e) => {
                this.settings.showCompleted = !this.settings.showCompleted;
                e.target.textContent = this.settings.showCompleted ? 'Hide Completed' : 'Show Completed';
                this.updateUI();
                this.saveSettings();
            });
        }

        // Sidebar mode
        const sidebarBtnEl = document.getElementById('sidebar-btn');
        if (sidebarBtnEl) {
            sidebarBtnEl.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Settings form
        const defaultModeEl = document.getElementById('default-mode');
        if (defaultModeEl) {
            defaultModeEl.addEventListener('change', (e) => {
                this.settings.defaultMode = e.target.value;
                this.saveSettings();
            });
        }

        const fullscreenLayoutEl = document.getElementById('fullscreen-layout');
        if (fullscreenLayoutEl) {
            fullscreenLayoutEl.addEventListener('change', (e) => {
                this.settings.fullscreenLayout = e.target.value;
                this.saveSettings();
            });
        }

        const closePreviousModeEl = document.getElementById('close-previous-mode');
        if (closePreviousModeEl) {
            closePreviousModeEl.addEventListener('change', (e) => {
                this.settings.closePreviousMode = e.target.checked;
                this.saveSettings();
            });
        }

        // Date picker modal
        const closeDatePickerEl = document.getElementById('close-date-picker');
        if (closeDatePickerEl) {
            closeDatePickerEl.addEventListener('click', () => {
                const modalEl = document.getElementById('date-picker-modal');
                if (modalEl) {
                    modalEl.style.display = 'none';
                }
            });
        }

        // Quick date options
        document.querySelectorAll('.quick-date-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const days = e.target.dataset.days;
                const endOfWeek = e.target.dataset.endOfWeek;
                let targetDate;

                if (days) {
                    targetDate = new Date();
                    targetDate.setDate(targetDate.getDate() + parseInt(days));
                } else if (endOfWeek) {
                    targetDate = new Date();
                    const dayOfWeek = targetDate.getDay();
                    const daysUntilFriday = (5 - dayOfWeek + 7) % 7;
                    targetDate.setDate(targetDate.getDate() + (daysUntilFriday === 0 ? 7 : daysUntilFriday));
                }

                if (targetDate) {
                    const dateString = targetDate.toISOString().split('T')[0];
                    const datePickerInput = document.getElementById('date-picker-input');
                    if (datePickerInput) {
                        datePickerInput.value = dateString;
                    }
                }
            });
        });

        const setDateBtnEl = document.getElementById('set-date-btn');
        if (setDateBtnEl) {
            setDateBtnEl.addEventListener('click', () => {
                this.setSelectedDate();
            });
        }

        const clearDateBtnEl = document.getElementById('clear-date-btn');
        if (clearDateBtnEl) {
            clearDateBtnEl.addEventListener('click', () => {
                this.clearSelectedDate();
            });
        }

        // Date picker input - allow Enter key to save
        const datePickerInputEl = document.getElementById('date-picker-input');
        if (datePickerInputEl) {
            datePickerInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.setSelectedDate();
                }
            });
        }

        // Collapsible sections
        document.querySelectorAll('.section-header.collapsible').forEach(header => {
            header.addEventListener('click', (e) => {
                // Don't toggle if clicking on collapse button or new item button
                if (e.target.classList.contains('collapse-btn') ||
                    e.target.classList.contains('new-item-btn')) return;
                this.toggleSection(header.dataset.section);
            });
        });

        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sectionHeader = e.target.closest('.section-header') || e.target.closest('.completed-header');
                if (sectionHeader && sectionHeader.dataset.section) {
                    this.toggleSection(sectionHeader.dataset.section);
                }
            });
        });

        // Undo toast
        const undoBtnEl = document.getElementById('undo-btn');
        if (undoBtnEl) {
            undoBtnEl.addEventListener('click', () => {
                this.undoDelete();
            });
        }

        const closeToastEl = document.getElementById('close-toast');
        if (closeToastEl) {
            closeToastEl.addEventListener('click', () => {
                this.hideUndoToast();
            });
        }

        // Edit note modal
        const closeEditNoteEl = document.getElementById('close-edit-note');
        if (closeEditNoteEl) {
            closeEditNoteEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        const saveNoteBtnEl = document.getElementById('save-note-btn');
        if (saveNoteBtnEl) {
            saveNoteBtnEl.addEventListener('click', () => {
                this.saveEditedNote();
            });
        }

        const cancelEditNoteBtnEl = document.getElementById('cancel-edit-note-btn');
        if (cancelEditNoteBtnEl) {
            cancelEditNoteBtnEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        // Completed/Archived page
        const viewCompletedBtnEl = document.getElementById('view-completed-btn');
        if (viewCompletedBtnEl) {
            viewCompletedBtnEl.addEventListener('click', () => {
                this.showCompletedArchivedPage();
            });
        }

        const backToMainBtnEl = document.getElementById('back-to-main-btn');
        if (backToMainBtnEl) {
            backToMainBtnEl.addEventListener('click', () => {
                this.hideCompletedArchivedPage();
            });
        }

        // Fullscreen mode
        const fullscreenBtnEl = document.getElementById('fullscreen-btn');
        if (fullscreenBtnEl) {
            fullscreenBtnEl.addEventListener('click', () => {
                this.openFullscreen();
            });
        }

        // Floating window mode
        const floatingWindowBtnEl = document.getElementById('floating-window-btn');
        if (floatingWindowBtnEl) {
            floatingWindowBtnEl.addEventListener('click', () => {
                this.openFloatingWindow();
            });
        }

        // Close window button (for floating mode)
        const closeWindowBtnEl = document.getElementById('close-window-btn');
        if (closeWindowBtnEl) {
            closeWindowBtnEl.addEventListener('click', () => {
                window.close();
            });
        }

        // Global mode icon toggles (inline in forms)
        const globalModeIconTodoEl = document.getElementById('global-mode-icon-todo');
        const globalModeIconNoteEl = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodoEl) {
            globalModeIconTodoEl.addEventListener('click', () => {
                this.todoGlobalMode = !this.todoGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();
            });
        }

        if (globalModeIconNoteEl) {
            globalModeIconNoteEl.addEventListener('click', () => {
                this.noteGlobalMode = !this.noteGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();
            });
        }

        // Setup drag and drop
        this.setupDragAndDrop();
    }

    setupDragAndDrop() {
        // Add event listeners to the container for drag and drop
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        [todosContainer, notesContainer].forEach(container => {
            if (container) {
                container.addEventListener('dragstart', this.handleDragStart.bind(this));
                container.addEventListener('dragover', this.handleDragOver.bind(this));
                container.addEventListener('drop', this.handleDrop.bind(this));
                container.addEventListener('dragend', this.handleDragEnd.bind(this));
            }
        });
    }

    handleDragStart(e) {
        if (e.target.classList.contains('item')) {
            this.draggedElement = e.target;
            e.target.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', e.target.outerHTML);

            // Add drag-active class to containers
            const containers = document.querySelectorAll('.items-list');
            containers.forEach(container => {
                container.classList.add('drag-active');
            });
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        if (!this.draggedElement) return;

        // Remove any existing drop indicator
        this.removeDropIndicator();

        // Find the closest drop position
        const afterElement = this.getDragAfterElement(e.currentTarget, e.clientY);

        // Create and show drop indicator at the correct position
        this.createAndShowDropIndicator(e.currentTarget, afterElement);

        // Move the dragged element for preview (but don't commit yet)
        if (afterElement == null) {
            e.currentTarget.appendChild(this.draggedElement);
        } else {
            e.currentTarget.insertBefore(this.draggedElement, afterElement);
        }
    }

    handleDrop(e) {
        e.preventDefault();
        if (this.draggedElement) {
            this.reorderItems();
        }
        this.cleanupDragState();
    }

    handleDragEnd(e) {
        this.cleanupDragState();
    }

    cleanupDragState() {
        // Remove dragging class
        if (this.draggedElement) {
            this.draggedElement.classList.remove('dragging');
        }

        // Remove drag-active class from containers
        const containers = document.querySelectorAll('.items-list');
        containers.forEach(container => {
            container.classList.remove('drag-active');
        });

        // Remove drop indicator
        this.removeDropIndicator();

        this.draggedElement = null;
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    createAndShowDropIndicator(container, afterElement) {
        // Create the drop indicator
        const indicator = document.createElement('div');
        indicator.className = 'drop-indicator active';
        indicator.style.pointerEvents = 'none';

        // Insert the indicator at the correct position
        if (afterElement == null) {
            // Insert at the end
            container.appendChild(indicator);
        } else {
            // Insert before the afterElement
            container.insertBefore(indicator, afterElement);
        }

        // Store reference for easy removal
        this.currentDropIndicator = indicator;
    }

    removeDropIndicator() {
        if (this.currentDropIndicator) {
            this.currentDropIndicator.remove();
            this.currentDropIndicator = null;
        }
    }

    reorderItems() {
        // Get the new order from the DOM
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        if (todosContainer) {
            const todoElements = [...todosContainer.querySelectorAll('.item[data-todo-id]')];
            const newTodoOrder = todoElements.map(el => el.dataset.todoId);
            this.reorderTodos(newTodoOrder);
        }

        if (notesContainer) {
            const noteElements = [...notesContainer.querySelectorAll('.item[data-note-id]')];
            const newNoteOrder = noteElements.map(el => el.dataset.noteId);
            this.reorderNotes(newNoteOrder);
        }
    }

    reorderTodos(newOrder) {
        const reorderedTodos = [];
        newOrder.forEach(id => {
            const todo = this.todos.find(t => t.id === id);
            if (todo) reorderedTodos.push(todo);
        });

        // Add any todos not in the new order (shouldn't happen, but safety check)
        this.todos.forEach(todo => {
            if (!newOrder.includes(todo.id)) {
                reorderedTodos.push(todo);
            }
        });

        this.todos = reorderedTodos;
        this.saveData();
    }

    reorderNotes(newOrder) {
        const reorderedNotes = [];
        newOrder.forEach(id => {
            const note = this.notes.find(n => n.id === id);
            if (note) reorderedNotes.push(note);
        });

        // Add any notes not in the new order (shouldn't happen, but safety check)
        this.notes.forEach(note => {
            if (!newOrder.includes(note.id)) {
                reorderedNotes.push(note);
            }
        });

        this.notes = reorderedNotes;
        this.saveData();
    }

    switchScope(scopeName) {
        // Update scope buttons
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const scopeBtn = document.querySelector(`[data-scope="${scopeName}"]`);
        if (scopeBtn) {
            scopeBtn.classList.add('active');
        }

        this.currentScope = scopeName;

        // Show/hide grouping selector and update options based on scope
        const groupingSelector = document.getElementById('grouping-selector');
        const groupBySelect = document.getElementById('group-by');

        if (scopeName === 'all' || scopeName === 'global' || scopeName === 'website' || scopeName === 'current-tab') {
            groupingSelector.style.display = 'flex';

            // Update grouping options based on scope
            if (scopeName === 'website') {
                // Hide website option for website scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'current-tab') {
                // Hide website option for current-tab scope (since it's just one page)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'global') {
                // Hide website option for global scope (since global items aren't tied to websites)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else {
                // Show website option for all scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'block';
                }
            }

            // Handle Type option - only show for 'all' scope
            const typeOption = groupBySelect.querySelector('option[value="type"]');
            if (typeOption) {
                if (scopeName === 'all') {
                    typeOption.style.display = 'block';
                } else {
                    typeOption.style.display = 'none';
                    // If currently grouped by type, switch to none
                    if (this.currentGrouping === 'type') {
                        this.currentGrouping = 'none';
                        groupBySelect.value = 'none';
                    }
                }
            }
        } else {
            groupingSelector.style.display = 'none';
        }

        this.updateUI();
    }

    openSettings() {
        const modalEl = document.getElementById('settings-modal');
        const defaultModeEl = document.getElementById('default-mode');
        const fullscreenLayoutEl = document.getElementById('fullscreen-layout');
        const closePreviousModeEl = document.getElementById('close-previous-mode');

        if (modalEl) modalEl.style.display = 'flex';
        if (defaultModeEl) defaultModeEl.value = this.settings.defaultMode;
        if (fullscreenLayoutEl) fullscreenLayoutEl.value = this.settings.fullscreenLayout;
        if (closePreviousModeEl) closePreviousModeEl.checked = this.settings.closePreviousMode;
    }

    closeSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    toggleSidebar() {
        const isSidebarMode = document.body.classList.contains('sidebar-mode');

        if (isSidebarMode) {
            // Close sidebar
            this.closeSidebar();
        } else {
            // Open sidebar
            this.openSidebar();
        }
    }

    async openSidebar() {
        try {
            // If we're in floating mode and user wants to close previous mode
            if (document.body.classList.contains('floating-mode') && this.settings.closePreviousMode) {
                window.close();
            }

            // Get the current window first
            const currentWindow = await chrome.windows.getCurrent();
            // Use Chrome's Side Panel API with the actual window ID
            await chrome.sidePanel.open({ windowId: currentWindow.id });
        } catch (error) {
            console.error('Failed to open side panel:', error);
            // Fallback: just show a message to the user
            alert('Please use the Chrome side panel setting to enable sidebar mode. Go to chrome://extensions, find this extension, and click "Details" then enable "Allow in side panel".');
        }
    }

    closeSidebar() {
        // Close the current window if we're in sidebar mode
        if (document.body.classList.contains('sidebar-mode')) {
            window.close();
        }
    }



    checkSidebarMode() {
        // Check if we're in sidebar mode based on URL parameters or window characteristics
        const urlParams = new URLSearchParams(window.location.search);
        const isSidebar = urlParams.get('sidebar') === 'true';

        // Better detection for Chrome's native side panel
        // Side panels typically have specific characteristics:
        // 1. They're chrome-extension protocol
        // 2. They have a constrained width (usually around 320-400px)
        // 3. They don't have window controls (no close button in window frame)
        // 4. The window.chrome.sidePanel API is available
        const isNativeSidePanel = window.location.protocol === 'chrome-extension:' &&
                                  window.innerWidth <= 500 &&
                                  window.innerWidth >= 280 &&
                                  // Check if we're likely in a side panel context
                                  (typeof chrome !== 'undefined' && chrome.sidePanel);

        // Check if we're in floating window mode (explicit parameter or window characteristics)
        const isFloatingWindow = window.location.search.includes('floating=true') ||
                                // Floating windows are typically larger and have specific dimensions
                                (window.outerWidth >= 350 && window.outerWidth <= 500 &&
                                 window.outerHeight >= 500 && window.outerHeight <= 800 &&
                                 !isNativeSidePanel);

        console.log('Mode detection:', {
            isSidebar,
            isNativeSidePanel,
            isFloatingWindow,
            innerWidth: window.innerWidth,
            outerWidth: window.outerWidth,
            protocol: window.location.protocol,
            search: window.location.search,
            hasSidePanelAPI: typeof chrome !== 'undefined' && chrome.sidePanel
        });

        if (isSidebar || isNativeSidePanel) {
            // Add sidebar-specific styling
            document.body.classList.add('sidebar-mode');
            document.body.classList.remove('floating-mode');
            this.updateButtonVisibility('sidebar');

            // Set up auto-refresh for sidebar mode
            this.setupSidebarRefresh();
            console.log('Sidebar mode activated');
        } else if (isFloatingWindow) {
            document.body.classList.add('floating-mode');
            document.body.classList.remove('sidebar-mode');
            this.updateButtonVisibility('floating');
            console.log('Floating mode activated');
        } else {
            // Normal popup mode
            document.body.classList.remove('sidebar-mode', 'floating-mode');
            this.updateButtonVisibility('popup');
            console.log('Popup mode activated');
        }
    }

    updateButtonVisibility(mode) {
        const sidebarBtn = document.getElementById('sidebar-btn');
        const floatingBtn = document.getElementById('floating-window-btn');
        const closeWindowBtn = document.getElementById('close-window-btn');

        if (mode === 'sidebar') {
            // In sidebar mode: show close button instead of sidebar button, hide floating button and close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '×';
                sidebarBtn.title = 'Close Sidebar';
            }
            if (floatingBtn) floatingBtn.style.display = 'none';
            if (closeWindowBtn) closeWindowBtn.style.display = 'none';
        } else if (mode === 'floating') {
            // In floating mode: show sidebar button, hide floating button, show close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '▶️';
                sidebarBtn.title = 'Open in Sidebar';
            }
            if (floatingBtn) floatingBtn.style.display = 'none';
            if (closeWindowBtn) {
                closeWindowBtn.style.display = 'inline-flex';
                closeWindowBtn.innerHTML = '×';
                closeWindowBtn.title = 'Close Window';
            }
        } else {
            // In popup mode: show both buttons, hide close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '▶️';
                sidebarBtn.title = 'Open in Sidebar';
            }
            if (floatingBtn) {
                floatingBtn.style.display = 'inline-flex';
            }
            if (closeWindowBtn) closeWindowBtn.style.display = 'none';
        }
    }

    setupSidebarRefresh() {
        // Listen for messages from background script about tab changes
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'tabChanged' || message.action === 'activeTabChanged') {
                // Refresh current tab info and update UI
                this.loadCurrentTab().then(() => {
                    this.updateUI();
                });
            }
        });

        // Listen for tab changes directly (backup method)
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async () => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Listen for tab changes in sidebar mode
        if (!document.body.classList.contains('fullscreen')) {
            this.setupTabChangeListener();
        }
    }

    setupTabChangeListener() {
        // Use Chrome's tab API to listen for tab changes
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async (activeInfo) => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Also listen for tab updates (URL changes within same tab)
        if (chrome.tabs && chrome.tabs.onUpdated) {
            chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
                if (changeInfo.status === 'complete' && tab.active) {
                    await this.loadCurrentTab();
                    this.updateUI();
                }
            });
        }

        // Fallback polling for cases where events don't fire
        setInterval(async () => {
            const newTab = await this.getCurrentTabInfo();
            if (newTab && this.currentTab && newTab.url !== this.currentTab.url) {
                await this.loadCurrentTab();
                this.updateUI();
            }
        }, 1000); // Check every second as fallback
    }

    async getCurrentTabInfo() {
        try {
            return await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
        } catch (error) {
            console.error('Error getting current tab info:', error);
            return null;
        }
    }

    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.todoGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in todo global mode
            baseUrl: this.getScopeBaseUrl(),
            notes: '' // Add notes field for todo-note relationships
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddTodo();
    }




    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!content) return;

        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.noteGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in note global mode
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null // For linking notes to todos
        };

        this.notes.unshift(note); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddNote();
    }


    
    getScopeUrl() {
        // Always save full URL, but scope determines what we show
        return this.currentTab?.url || '';
    }

    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }

    getFilteredTodos() {
        const allTodos = this.todos;

        switch (this.currentScope) {
            case 'global':
                return allTodos.filter(todo => todo.url === 'global');
            case 'current-tab':
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allTodos.filter(todo => {
                    try {
                        if (todo.url === 'global') return false; // Exclude global todos from website view
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allTodos; // Include all todos including global ones
            default:
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
        }
    }

    getFilteredNotes() {
        const allNotes = this.notes.filter(note => !note.archived);

        switch (this.currentScope) {
            case 'global':
                return allNotes.filter(note => note.url === 'global');
            case 'current-tab':
                return allNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allNotes.filter(note => {
                    try {
                        if (note.url === 'global') return false; // Exclude global notes from website view
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allNotes; // Include all notes including global ones
            default:
                return allNotes.filter(note => note.url === this.currentTab?.url);
        }
    }

    updateUI() {
        // Skip regular UI updates in fullscreen mode
        if (document.body.classList.contains('fullscreen')) {
            return;
        }

        this.updateTodosList();
        this.updateNotesList();
        this.updateCounts();

        // Update placeholder text based on current scope and per-item global mode
        this.updatePlaceholders();

        this.updateGlobalModeDisplay();
    }

    updatePlaceholders() {
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        // Update todo placeholder based on per-item global mode
        if (todoInput) {
            if (this.todoGlobalMode) {
                todoInput.placeholder = 'Add a global todo (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        todoInput.placeholder = 'Add a new todo for this page...';
                        break;
                    case 'website':
                        todoInput.placeholder = 'Add a new todo for this website...';
                        break;
                    case 'all':
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                    default:
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                }
            }
        }

        // Update note placeholder based on per-item global mode
        if (noteContent) {
            if (this.noteGlobalMode) {
                noteContent.placeholder = 'Add a global note (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        noteContent.placeholder = 'Add a new note for this page...';
                        break;
                    case 'website':
                        noteContent.placeholder = 'Add a new note for this website...';
                        break;
                    case 'all':
                        noteContent.placeholder = 'Add a new note...';
                        break;
                    default:
                        noteContent.placeholder = 'Add a new note...';
                        break;
                }
            }
        }
    }

    updateGlobalModeDisplay() {
        this.updateGlobalModeIcons();

        // Disable scope selector when in global mode
        const scopeSelector = document.getElementById('scope-selector');
        if (scopeSelector) {
            scopeSelector.disabled = this.globalMode;
            scopeSelector.style.opacity = this.globalMode ? '0.5' : '1';
        }
    }

    updateGlobalModeIcons() {
        const globalModeIconTodo = document.getElementById('global-mode-icon-todo');
        const globalModeIconNote = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodo) {
            if (this.todoGlobalMode) {
                globalModeIconTodo.classList.add('active');
            } else {
                globalModeIconTodo.classList.remove('active');
            }
        }

        if (globalModeIconNote) {
            if (this.noteGlobalMode) {
                globalModeIconNote.classList.add('active');
            } else {
                globalModeIconNote.classList.remove('active');
            }
        }
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'All Items': items };
        }

        const groups = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'website':
                    try {
                        const url = new URL(item.url);
                        // Extract base URL (hostname + path without query/hash)
                        groupKey = url.hostname + (url.pathname !== '/' ? url.pathname : '');
                    } catch (error) {
                        groupKey = 'Global';
                    }
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        const date = new Date(item.dueDate);
                        groupKey = date.toDateString();
                    } else {
                        groupKey = 'No Due Date';
                    }
                    break;
                case 'created-date':
                    const createdDate = new Date(item.createdAt);
                    groupKey = createdDate.toDateString();
                    break;
                case 'type':
                    groupKey = item.url === 'global' ? 'Global' : 'Website-specific';
                    break;
                default:
                    groupKey = 'All Items';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
        });

        // Sort groups for due-date: items with due dates first, then no due date
        if (groupBy === 'due-date') {
            const sortedGroups = {};
            const withDueDates = [];
            const withoutDueDates = [];

            Object.keys(groups).forEach(key => {
                if (key === 'No Due Date') {
                    withoutDueDates.push(key);
                } else {
                    withDueDates.push(key);
                }
            });

            // Sort dates chronologically
            withDueDates.sort((a, b) => new Date(a) - new Date(b));

            // Add with due dates first, then without
            [...withDueDates, ...withoutDueDates].forEach(key => {
                sortedGroups[key] = groups[key];
            });

            return sortedGroups;
        }

        // Sort groups for type: website-specific first, then global
        if (groupBy === 'type') {
            const sortedGroups = {};
            const websiteSpecific = [];
            const global = [];

            Object.keys(groups).forEach(key => {
                if (key === 'Global') {
                    global.push(key);
                } else {
                    websiteSpecific.push(key);
                }
            });

            // Add website-specific first, then global
            [...websiteSpecific, ...global].forEach(key => {
                sortedGroups[key] = groups[key];
            });

            return sortedGroups;
        }

        return groups;
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const filteredTodos = this.getFilteredTodos().filter(todo => !todo.completed);

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '';
            return;
        }

        todosList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                todosList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredTodos.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });
        }
    }



    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '';
            return;
        }

        notesList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                notesList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredNotes.forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });
        }
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''}`;
        todoEl.draggable = true;
        todoEl.dataset.todoId = todo.id;
        todoEl.dataset.itemType = 'todo';

        const dueDateDisplay = this.formatDueDate(todo.dueDate);
        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';

        const faviconUrl = todo.url !== 'global' ? this.getFaviconUrl(todo.url) : null;

        todoEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
            <div class="item-content">
                <div class="item-text" data-todo-id="${todo.id}" style="cursor: pointer;">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">
                        ${faviconUrl ? `<img src="${faviconUrl}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}${todo.url === 'global' ? '🌐 ' : ''}${todo.text}
                </div>
                <div class="item-meta">
                    ${dueDateDisplay ? `<span class="item-due-date ${this.getDueDateClass(todo.dueDate)}">${dueDateDisplay}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-btn" data-todo-id="${todo.id}" title="Edit">✏️</button>
                <button class="date-btn" data-todo-id="${todo.id}" title="Set Due Date">📅</button>
                ${todo.url === 'global' ? `<button class="link-btn" data-todo-id="${todo.id}" title="Add URL">🔗</button>` : ''}
                <button class="delete-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const checkbox = todoEl.querySelector('.item-checkbox');
        checkbox.addEventListener('change', () => this.toggleTodo(todo.id));

        const textEl = todoEl.querySelector('.item-text');
        textEl.addEventListener('click', () => this.editTodo(todo.id));

        const editBtn = todoEl.querySelector('.edit-btn');
        editBtn.addEventListener('click', () => this.editTodo(todo.id));

        const dateBtn = todoEl.querySelector('.date-btn');
        dateBtn.addEventListener('click', () => this.setDueDate(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        // Add link button event listener for global todos
        const linkBtn = todoEl.querySelector('.link-btn');
        if (linkBtn) {
            linkBtn.addEventListener('click', () => this.addUrlToGlobalTodo(todo.id));
        }

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item';
        noteEl.draggable = true;
        noteEl.dataset.noteId = note.id;
        noteEl.dataset.itemType = 'note';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';

        const createdDate = new Date(note.createdAt).toLocaleDateString();

        noteEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">
                        ${this.getFaviconUrl(note.url) ? `<img src="${this.getFaviconUrl(note.url)}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}
                    ${note.title ? `<strong>${note.url === 'global' ? '🌐 ' : ''}${note.title}</strong> <span class="created-date">${createdDate}</span><br>` : ''}
                    ${note.url === 'global' && !note.title ? '🌐 ' : ''}${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                <button class="archive-note-btn" data-note-id="${note.id}" title="Archive">📁</button>
                ${note.url === 'global' ? `<button class="link-note-btn" data-note-id="${note.id}" title="Add URL">🔗</button>` : ''}
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const editBtn = noteEl.querySelector('.edit-note-btn');
        editBtn.addEventListener('click', () => this.editNote(note.id));

        const archiveBtn = noteEl.querySelector('.archive-note-btn');
        archiveBtn.addEventListener('click', () => this.archiveNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        // Add link button event listener for global notes
        const linkBtn = noteEl.querySelector('.link-note-btn');
        if (linkBtn) {
            linkBtn.addEventListener('click', () => this.addUrlToGlobalNote(note.id));
        }

        return noteEl;
    }



    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            case 'type':
                return groupKey === 'Global' ? `🌐 ${groupKey}` : `🔗 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    getFaviconUrl(url) {
        try {
            const urlObj = new URL(url);
            return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`;
        } catch (error) {
            return null;
        }
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else {
            return due.toLocaleDateString();
        }
    }

    getDueDateClass(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'tomorrow';
        } else {
            return '';
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveData();
            this.updateUI(); // Update both active and completed lists
        }
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            // Find the todo element
            const todoEl = document.querySelector(`[data-todo-id="${todoId}"]`);
            if (!todoEl) return;

            const textEl = todoEl.querySelector('.item-text');
            if (!textEl) return;

            // Check if already editing
            if (textEl.querySelector('input') || textEl.querySelector('.edit-form')) return;

            // Store original text
            const originalText = textEl.innerHTML;

            // Check if this is a global item that can have URL added
            const isGlobal = todo.url === 'global';

            if (isGlobal) {
                // Create enhanced edit form for global items with URL option
                this.createGlobalTodoEditForm(todo, textEl, originalText);
            } else {
                // Create simple text input for non-global items
                this.createSimpleTodoEdit(todo, textEl, originalText);
            }
        }
    }

    createSimpleTodoEdit(todo, textEl, originalText) {
        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = todo.text;
        input.className = 'inline-edit-input';

        // Replace text with input
        textEl.innerHTML = '';
        textEl.appendChild(input);
        input.focus();
        input.select();

        // Handle save
        const saveEdit = () => {
            const newText = input.value.trim();
            if (newText && newText !== todo.text) {
                todo.text = newText;
                this.saveData();
                this.updateUI();
            } else {
                // Restore original text if no changes or empty
                textEl.innerHTML = originalText;
            }
        };

        // Handle cancel
        const cancelEdit = () => {
            textEl.innerHTML = originalText;
        };

        // Event listeners
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        input.addEventListener('blur', () => {
            saveEdit();
        });
    }

    createGlobalTodoEditForm(todo, textEl, originalText) {
        // Create edit form container
        const editForm = document.createElement('div');
        editForm.className = 'edit-form global-edit-form';

        // Create text input
        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.value = todo.text;
        textInput.className = 'edit-text-input';
        textInput.placeholder = 'Todo text...';

        // Create URL input
        const urlInput = document.createElement('input');
        urlInput.type = 'url';
        urlInput.value = '';
        urlInput.className = 'edit-url-input';
        urlInput.placeholder = 'Add URL to convert to website-specific (optional)...';

        // Create action buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'edit-buttons';

        const saveBtn = document.createElement('button');
        saveBtn.className = 'edit-save-btn';
        saveBtn.innerHTML = '✓';
        saveBtn.title = 'Save changes';

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'edit-cancel-btn';
        cancelBtn.innerHTML = '✖';
        cancelBtn.title = 'Cancel editing';

        buttonContainer.appendChild(saveBtn);
        buttonContainer.appendChild(cancelBtn);

        // Assemble form
        editForm.appendChild(textInput);
        editForm.appendChild(urlInput);
        editForm.appendChild(buttonContainer);

        // Replace text with form
        textEl.innerHTML = '';
        textEl.appendChild(editForm);
        textInput.focus();
        textInput.select();

        // Handle save
        const saveEdit = () => {
            const newText = textInput.value.trim();
            const newUrl = urlInput.value.trim();

            if (!newText) {
                // Restore original if text is empty
                textEl.innerHTML = originalText;
                return;
            }

            let hasChanges = false;

            // Update text if changed
            if (newText !== todo.text) {
                todo.text = newText;
                hasChanges = true;
            }

            // Update URL if provided (convert from global to website-specific)
            if (newUrl) {
                try {
                    // Validate URL
                    new URL(newUrl);
                    todo.url = newUrl;
                    hasChanges = true;
                } catch (e) {
                    // Invalid URL, show error and don't save
                    urlInput.style.borderColor = '#ef4444';
                    urlInput.focus();
                    return;
                }
            }

            if (hasChanges) {
                this.saveData();
                this.updateUI();
            } else {
                // Restore original text if no changes
                textEl.innerHTML = originalText;
            }
        };

        // Handle cancel
        const cancelEdit = () => {
            textEl.innerHTML = originalText;
        };

        // Event listeners
        saveBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            saveEdit();
        });

        cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            cancelEdit();
        });

        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        urlInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        // Clear URL input error styling on input
        urlInput.addEventListener('input', () => {
            urlInput.style.borderColor = '';
        });
    }

    setDueDate(todoId) {
        this.currentEditingTodoId = todoId;
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const currentDate = todo.dueDate || '';
            document.getElementById('date-picker-input').value = currentDate;
            document.getElementById('date-picker-modal').style.display = 'flex';
        }
    }

    deleteTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Todo deleted', todoBackup.text, () => {
                this.todos.push(todoBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            this.currentEditingNoteId = noteId;

            const titleEl = document.getElementById('edit-note-title');
            const contentEl = document.getElementById('edit-note-content');
            const urlEl = document.getElementById('edit-note-url');
            const urlSectionEl = document.getElementById('edit-note-url-section');
            const modalEl = document.getElementById('edit-note-modal');

            if (titleEl) titleEl.value = note.title;
            if (contentEl) contentEl.value = note.content;

            // Show URL section only for global notes
            if (note.url === 'global') {
                if (urlSectionEl) urlSectionEl.style.display = 'block';
                if (urlEl) {
                    urlEl.value = '';
                    urlEl.style.borderColor = '';
                }
            } else {
                if (urlSectionEl) urlSectionEl.style.display = 'none';
            }

            if (modalEl) modalEl.style.display = 'flex';
        }
    }

    closeEditNoteModal() {
        const modalEl = document.getElementById('edit-note-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
        this.currentEditingNoteId = null;
    }

    saveEditedNote() {
        if (!this.currentEditingNoteId) return;

        const note = this.notes.find(n => n.id === this.currentEditingNoteId);
        if (!note) return;

        const titleEl = document.getElementById('edit-note-title');
        const contentEl = document.getElementById('edit-note-content');
        const urlEl = document.getElementById('edit-note-url');

        const newTitle = titleEl ? titleEl.value.trim() : '';
        const newContent = contentEl ? contentEl.value.trim() : '';
        const newUrl = urlEl ? urlEl.value.trim() : '';

        if (!newContent) {
            // Don't save if content is empty
            this.closeEditNoteModal();
            return;
        }

        // Update title and content
        note.title = newTitle || newContent.split('\n')[0].substring(0, 50);
        note.content = newContent;

        // Handle URL change for global notes
        if (note.url === 'global' && newUrl) {
            try {
                // Validate URL
                new URL(newUrl);
                note.url = newUrl;
            } catch (e) {
                // Invalid URL, show error and don't save
                if (urlEl) {
                    urlEl.style.borderColor = '#ef4444';
                    urlEl.focus();
                }
                return;
            }
        }

        this.saveData();
        this.updateUI();
        this.closeEditNoteModal();
    }

    linkToTodo(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const availableTodos = this.getFilteredTodos();
            if (availableTodos.length === 0) {
                alert('No todos available to link to. Create a todo first.');
                return;
            }

            const todoOptions = availableTodos.map(todo => `${todo.id}: ${todo.text}`).join('\n');
            const selectedTodoId = prompt(`Link to which todo?\n\n${todoOptions}\n\nEnter todo ID:`);

            if (selectedTodoId && availableTodos.find(t => t.id === selectedTodoId)) {
                note.linkedTodoId = selectedTodoId;
                this.saveData();
                this.updateUI();
                alert('Note linked to todo successfully!');
            }
        }
    }

    archiveNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            note.archived = true;
            note.archivedAt = new Date().toISOString();
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note archived', note.title || note.content.substring(0, 50), () => {
                const restoredNote = this.notes.find(n => n.id === noteId);
                if (restoredNote) {
                    restoredNote.archived = false;
                    delete restoredNote.archivedAt;
                    this.saveData();
                    this.updateUI();
                }
            });
        }
    }

    deleteNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note deleted', noteBackup.title || noteBackup.content.substring(0, 50), () => {
                this.notes.push(noteBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    saveSettings() {
        console.log('Saving settings:', this.settings);
        chrome.storage.local.set({
            settings: this.settings
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // New UI methods
    showAddTodo() {
        // Ensure todos section is expanded
        const todosSection = document.querySelector('.todos-section');
        const todosContent = todosSection.querySelector('.section-content');
        if (todosContent) {
            todosContent.style.display = 'block';
            todosSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-todo-section').style.display = 'block';
        const todoInput = document.getElementById('todo-input');
        todoInput.focus();

        // Scroll to the form
        document.getElementById('add-todo-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddTodo() {
        document.getElementById('add-todo-section').style.display = 'none';
        document.getElementById('todo-input').value = '';
        document.getElementById('todo-due-date').style.display = 'none';
        document.getElementById('todo-due-date').value = '';

        // Reset per-item global mode
        this.todoGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    handleTodoEscape() {
        const input = document.getElementById('todo-input');
        const text = input.value.trim();

        if (!text) {
            // If empty, just hide the input
            this.hideAddTodo();
        } else {
            // If has text, clear it and hide
            input.value = '';
            this.hideAddTodo();
        }
    }

    showAddNote() {
        // Ensure notes section is expanded
        const notesSection = document.querySelector('.notes-section');
        const notesContent = notesSection.querySelector('.section-content');
        if (notesContent) {
            notesContent.style.display = 'block';
            notesSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-note-section').style.display = 'block';
        const noteTitle = document.getElementById('note-title');
        noteTitle.focus();

        // Scroll to the form
        document.getElementById('add-note-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddNote() {
        document.getElementById('add-note-section').style.display = 'none';
        document.getElementById('note-title').value = '';
        document.getElementById('note-content').value = '';

        // Reset per-item global mode
        this.noteGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    handleNoteEscape() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!title && !content) {
            // If both empty, just hide the input
            this.hideAddNote();
        } else {
            // If has text, clear it and hide
            titleInput.value = '';
            contentInput.value = '';
            this.hideAddNote();
        }
    }

    addUrlToGlobalTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo || todo.url !== 'global') return;

        const url = prompt('Enter URL to link this todo to:', 'https://');
        if (url && url.trim() && url !== 'https://') {
            const cleanUrl = url.trim();

            // Validate URL
            try {
                new URL(cleanUrl);

                // Update the todo
                todo.url = cleanUrl;
                todo.baseUrl = this.extractBaseUrl(cleanUrl);

                this.saveData();
                this.updateUI();

                // Show success message
                this.showToast('URL added successfully! Todo moved to website-specific section.', 'success');
            } catch (error) {
                this.showToast('Invalid URL. Please enter a valid URL.', 'error');
            }
        }
    }

    addUrlToGlobalNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (!note || note.url !== 'global') return;

        const url = prompt('Enter URL to link this note to:', 'https://');
        if (url && url.trim() && url !== 'https://') {
            const cleanUrl = url.trim();

            // Validate URL
            try {
                new URL(cleanUrl);

                // Update the note
                note.url = cleanUrl;
                note.baseUrl = this.extractBaseUrl(cleanUrl);

                this.saveData();
                this.updateUI();

                // Show success message
                this.showToast('URL added successfully! Note moved to website-specific section.', 'success');
            } catch (error) {
                this.showToast('Invalid URL. Please enter a valid URL.', 'error');
            }
        }
    }

    extractBaseUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}`;
        } catch (error) {
            console.error('Error extracting base URL:', error);
            return url;
        }
    }

    async setTheme(theme) {
        // Save theme preference
        await chrome.storage.local.set({ theme: theme });

        // Apply theme to document
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.removeAttribute('data-theme');
        }

        console.log('Theme set to:', theme);
    }

    async loadTheme() {
        try {
            const result = await chrome.storage.local.get(['theme']);
            const theme = result.theme || 'light';

            // Apply theme to document
            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                document.documentElement.removeAttribute('data-theme');
            }

            // Update theme selector
            const themeSelect = document.getElementById('theme-select');
            if (themeSelect) {
                themeSelect.value = theme;
            }

            console.log('Theme loaded:', theme);
        } catch (error) {
            console.error('Error loading theme:', error);
        }
    }

    showDatePicker() {
        const todoDueDateEl = document.getElementById('todo-due-date');
        const datePickerInputEl = document.getElementById('date-picker-input');
        const modalEl = document.getElementById('date-picker-modal');

        const currentDate = todoDueDateEl ? todoDueDateEl.value : '';
        if (datePickerInputEl) {
            datePickerInputEl.value = currentDate;
        }
        if (modalEl) {
            modalEl.style.display = 'flex';
        }
    }

    setSelectedDate() {
        const datePickerInput = document.getElementById('date-picker-input');
        const selectedDate = datePickerInput ? datePickerInput.value : '';

        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = selectedDate || null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');

            if (todoDueDateEl) {
                todoDueDateEl.value = selectedDate;
                todoDueDateEl.style.display = selectedDate ? 'inline-block' : 'none';
            }

            // Return focus to the todo input after setting date
            const todoInputEl = document.getElementById('todo-input');
            if (todoInputEl) {
                setTimeout(() => {
                    todoInputEl.focus();
                }, 100); // Small delay to ensure modal is closed first
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    clearSelectedDate() {
        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');
            if (todoDueDateEl) {
                todoDueDateEl.value = '';
                todoDueDateEl.style.display = 'none';
            }

            // Return focus to the todo input after clearing date
            const todoInputEl = document.getElementById('todo-input');
            if (todoInputEl) {
                setTimeout(() => {
                    todoInputEl.focus();
                }, 100); // Small delay to ensure modal is closed first
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    toggleSection(sectionName) {
        const content = document.getElementById(`${sectionName}-content`);
        const btn = document.querySelector(`[data-section="${sectionName}"] .collapse-btn`);

        if (!content || !btn) return;

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            btn.classList.remove('collapsed');
            btn.textContent = '▼';
        } else {
            content.classList.add('collapsed');
            btn.classList.add('collapsed');
            btn.textContent = '▶';
        }
    }



    showUndoToast(message, itemText, undoCallback) {
        this.undoCallback = undoCallback;
        const titleEl = document.getElementById('undo-title');
        const itemTextEl = document.getElementById('undo-item-text');
        const toastEl = document.getElementById('undo-toast');
        const progressBarEl = document.getElementById('toast-progress-bar');

        // Set the title based on the message type
        if (titleEl) {
            if (message.includes('Todo')) {
                titleEl.textContent = 'ℹ Deleted Todo';
            } else if (message.includes('Note')) {
                titleEl.textContent = message.includes('archived') ? 'ℹ Archived Note' : 'ℹ Deleted Note';
            }
        }

        // Set the item text
        if (itemTextEl) itemTextEl.textContent = itemText;
        if (toastEl) toastEl.style.display = 'block';

        // Reset progress bar
        if (progressBarEl) {
            progressBarEl.style.width = '100%';
        }

        // Clear any existing timeout
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start progress bar animation
        let timeLeft = 20000; // 20 seconds
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            timeLeft -= updateInterval;
            const percentage = (timeLeft / 20000) * 100;

            if (progressBarEl) {
                progressBarEl.style.width = `${Math.max(0, percentage)}%`;
            }

            if (timeLeft <= 0) {
                this.hideUndoToast();
            }
        }, updateInterval);

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    hideUndoToast() {
        const toastEl = document.getElementById('undo-toast');
        if (toastEl) {
            toastEl.style.display = 'none';
        }

        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
            this.undoTimeout = null;
        }

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        this.undoCallback = null;
    }

    undoDelete() {
        if (this.undoCallback) {
            this.undoCallback();
            this.hideUndoToast();
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification-toast');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification-toast';
            notification.className = 'notification-toast';
            document.body.appendChild(notification);
        }

        notification.textContent = message;
        notification.className = `notification-toast ${type}`;
        notification.style.display = 'block';

        // Auto-hide after duration
        setTimeout(() => {
            notification.style.display = 'none';
        }, duration);
    }

    updateCounts() {
        const filteredTodos = this.getFilteredTodos();
        const completedTodos = filteredTodos.filter(todo => todo.completed);
        const activeTodos = filteredTodos.filter(todo => !todo.completed);
        const filteredNotes = this.getFilteredNotes();

        document.getElementById('todos-count').textContent = `(${activeTodos.length})`;
        document.getElementById('notes-count').textContent = `(${filteredNotes.length})`;
    }

    showCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'none';
        if (completedPage) completedPage.style.display = 'block';

        this.updateCompletedArchivedPage();
    }

    hideCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'block';
        if (completedPage) completedPage.style.display = 'none';
    }

    updateCompletedArchivedPage() {
        // Update completed todos
        const completedTodos = this.todos.filter(todo => todo.completed);
        const completedPageList = document.getElementById('completed-page-list');
        const completedPageCount = document.getElementById('completed-page-count');

        if (completedPageList) {
            completedPageList.innerHTML = '';
            completedTodos.forEach(todo => {
                const todoEl = this.createCompletedTodoElement(todo);
                completedPageList.appendChild(todoEl);
            });
        }

        if (completedPageCount) {
            completedPageCount.textContent = `(${completedTodos.length})`;
        }

        // Update archived notes
        const archivedNotes = this.notes.filter(note => note.archived);
        const archivedPageList = document.getElementById('archived-page-list');
        const archivedPageCount = document.getElementById('archived-page-count');

        if (archivedPageList) {
            archivedPageList.innerHTML = '';
            archivedNotes.forEach(note => {
                const noteEl = this.createArchivedNoteElement(note);
                archivedPageList.appendChild(noteEl);
            });
        }

        if (archivedPageCount) {
            archivedPageCount.textContent = `(${archivedNotes.length})`;
        }
    }

    createCompletedTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = 'item completed';

        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';
        const completedDate = todo.completedAt ? new Date(todo.completedAt).toLocaleDateString() : '';

        todoEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">🔗</a> ` : ''}
                    <span class="todo-text">${todo.text}</span>
                    ${completedDate ? `<span class="completed-date"> - Completed ${completedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-todo-btn" data-todo-id="${todo.id}" title="Restore">↩️</button>
                <button class="delete-todo-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = todoEl.querySelector('.restore-todo-btn');
        restoreBtn.addEventListener('click', () => this.restoreTodo(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-todo-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createArchivedNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item archived';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';
        const archivedDate = note.archivedAt ? new Date(note.archivedAt).toLocaleDateString() : '';

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">🔗</a> ` : ''}
                    ${note.title ? `<strong>${note.title}</strong><br>` : ''}
                    ${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                    ${archivedDate ? `<span class="archived-date"> - Archived ${archivedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-note-btn" data-note-id="${note.id}" title="Restore">↩️</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = noteEl.querySelector('.restore-note-btn');
        restoreBtn.addEventListener('click', () => this.restoreNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }

    restoreTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = false;
            delete todo.completedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    restoreNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            note.archived = false;
            delete note.archivedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    openFullscreen() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('fullscreen.html')
        });
    }

    openFloatingWindow() {
        // If we're in sidebar mode and user wants to close previous mode
        if (document.body.classList.contains('sidebar-mode') && this.settings.closePreviousMode) {
            window.close();
        }

        chrome.windows.create({
            url: chrome.runtime.getURL('popup.html?floating=true'),
            type: 'popup',
            width: 400,
            height: 600,
            focused: true
        });
    }

    initFullscreenMode() {
        // Check if we're in fullscreen mode
        if (!document.body.classList.contains('fullscreen')) {
            return;
        }

        // Set up fullscreen-specific event listeners
        const addGlobalTodoBtnEl = document.getElementById('add-global-todo-btn');
        if (addGlobalTodoBtnEl) {
            addGlobalTodoBtnEl.addEventListener('click', () => {
                this.addGlobalTodo();
            });
        }

        const addGlobalNoteBtnEl = document.getElementById('add-global-note-btn');
        if (addGlobalNoteBtnEl) {
            addGlobalNoteBtnEl.addEventListener('click', () => {
                this.addGlobalNote();
            });
        }

        // Load and display global items
        this.loadData().then(() => {
            // Apply layout based on setting
            if (this.settings.fullscreenLayout === 'vertical') {
                document.body.classList.add('vertical-split');
            } else {
                document.body.classList.remove('vertical-split');
            }

            this.updateGlobalTodosList();
            this.updateGlobalNotesList();
        });
    }

    addGlobalTodo() {
        const textEl = document.getElementById('global-todo-text');
        const dateEl = document.getElementById('global-todo-date');
        const urlEl = document.getElementById('global-todo-url');

        const text = textEl ? textEl.value.trim() : '';
        const dueDate = dateEl ? dateEl.value : '';
        const url = urlEl ? urlEl.value.trim() : '';

        if (text) {
            const todo = {
                id: Date.now().toString(),
                text: text,
                completed: false,
                url: url || 'global',
                createdAt: new Date().toISOString(),
                dueDate: dueDate || null
            };

            this.todos.unshift(todo);
            this.saveData();
            this.updateGlobalTodosList();

            // Clear form
            if (textEl) textEl.value = '';
            if (dateEl) dateEl.value = '';
            if (urlEl) urlEl.value = '';
        }
    }

    addGlobalNote() {
        const titleEl = document.getElementById('global-note-title');
        const contentEl = document.getElementById('global-note-content');
        const urlEl = document.getElementById('global-note-url');

        const title = titleEl ? titleEl.value.trim() : '';
        const content = contentEl ? contentEl.value.trim() : '';
        const url = urlEl ? urlEl.value.trim() : '';

        if (content) {
            const note = {
                id: Date.now().toString(),
                title: title || content.split('\n')[0].substring(0, 50),
                content: content,
                url: url || 'global',
                createdAt: new Date().toISOString()
            };

            this.notes.unshift(note);
            this.saveData();
            this.updateGlobalNotesList();

            // Clear form
            if (titleEl) titleEl.value = '';
            if (contentEl) contentEl.value = '';
            if (urlEl) urlEl.value = '';
        }
    }

    updateGlobalTodosList() {
        const listEl = document.getElementById('global-todos-list');
        if (!listEl) return;

        const globalTodos = this.todos.filter(todo => todo.url === 'global' && !todo.completed);
        listEl.innerHTML = '';

        globalTodos.forEach(todo => {
            const todoEl = this.createTodoElement(todo);
            listEl.appendChild(todoEl);
        });
    }

    updateGlobalNotesList() {
        const listEl = document.getElementById('global-notes-list');
        if (!listEl) return;

        const globalNotes = this.notes.filter(note => note.url === 'global' && !note.archived);
        listEl.innerHTML = '';

        globalNotes.forEach(note => {
            const noteEl = this.createNoteElement(note);
            listEl.appendChild(noteEl);
        });
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
    // Make app globally available for inline event handlers
    window.app = app;
});
