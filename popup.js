// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'all'; // Default to all view
        this.currentGrouping = 'none';
        this.globalMode = false; // Keep for backward compatibility, but will be phased out
        this.currentEditingTodoId = null;

        // Per-item global mode states
        this.todoGlobalMode = false;
        this.noteGlobalMode = false;
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        this.settings = {
            sidebarMode: true, // Default to sidebar mode
            showCompleted: true,
            verticalSplitMode: false,
            floatingWindowMode: false
        };

        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();

        // Skip sidebar mode check in fullscreen
        if (!document.body.classList.contains('fullscreen')) {
            this.checkSidebarMode();
        } else {
            // In fullscreen mode, default to global mode
            this.globalMode = true;
            this.updateGlobalModeDisplay();
        }

        this.updateUI();

        // Ensure counts are updated after everything is loaded (skip in fullscreen)
        if (!document.body.classList.contains('fullscreen')) {
            setTimeout(() => {
                this.updateCounts();
            }, 100);
        }
    }
    
    async loadCurrentTab() {
        try {
            // Skip tab loading in fullscreen mode
            if (document.body.classList.contains('fullscreen')) {
                this.currentTab = null;
                return;
            }

            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });

            if (this.currentTab) {
                const currentUrlEl = document.getElementById('current-url');
                if (currentUrlEl) {
                    currentUrlEl.textContent = this.formatUrl(this.currentTab.url);
                }
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache', 'settings'], resolve);
            });

            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
            this.settings = { ...this.settings, ...(result.settings || {}) };
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Scope selection
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchScope(e.target.dataset.scope);
            });
        });

        // Grouping selection
        const groupByEl = document.getElementById('group-by');
        if (groupByEl) {
            groupByEl.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;
                this.updateUI();
            });
        }

        // New item buttons
        const newTodoBtnEl = document.getElementById('new-todo-btn');
        if (newTodoBtnEl) {
            newTodoBtnEl.addEventListener('click', () => {
                this.showAddTodo();
            });
        }

        const newNoteBtnEl = document.getElementById('new-note-btn');
        if (newNoteBtnEl) {
            newNoteBtnEl.addEventListener('click', () => {
                this.showAddNote();
            });
        }

        // Add todo - removed button, now using auto-save

        // Enhanced auto-save for todo input
        const todoInputEl = document.getElementById('todo-input');
        if (todoInputEl) {
            let currentAutoSavedTodoId = null;

            // Save immediately when user starts typing
            todoInputEl.addEventListener('input', () => {
                const text = todoInputEl.value.trim();
                if (text && !currentAutoSavedTodoId) {
                    // Create todo immediately when user starts typing
                    currentAutoSavedTodoId = this.createAutoSavedTodo(text);
                } else if (text && currentAutoSavedTodoId) {
                    // Update existing auto-saved todo
                    this.updateAutoSavedTodo(currentAutoSavedTodoId, text);
                }
            });

            // Handle Enter key
            todoInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const text = todoInputEl.value.trim();
                    if (text && currentAutoSavedTodoId) {
                        // Finalize the auto-saved todo
                        this.finalizeAutoSavedTodo(currentAutoSavedTodoId);
                        currentAutoSavedTodoId = null;
                        this.hideAddTodo();
                    }
                }
            });

            // Handle ESC key - delete if empty
            todoInputEl.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    const text = todoInputEl.value.trim();
                    if (!text && currentAutoSavedTodoId) {
                        this.deleteAutoSavedTodo(currentAutoSavedTodoId);
                    }
                    currentAutoSavedTodoId = null;
                    this.hideAddTodo();
                }
            });

            // Handle blur (clicking outside) - delete if empty
            todoInputEl.addEventListener('blur', () => {
                setTimeout(() => {
                    const text = todoInputEl.value.trim();
                    if (!text && currentAutoSavedTodoId) {
                        this.deleteAutoSavedTodo(currentAutoSavedTodoId);
                        currentAutoSavedTodoId = null;
                        this.hideAddTodo();
                    }
                }, 100); // Small delay to allow other click events to process
            });
        }

        const cancelTodoBtnEl = document.getElementById('cancel-todo-btn');
        if (cancelTodoBtnEl) {
            cancelTodoBtnEl.addEventListener('click', () => {
                this.hideAddTodo();
            });
        }

        // Calendar button (only exists in popup mode)
        const calendarBtnEl = document.getElementById('calendar-btn');
        if (calendarBtnEl) {
            calendarBtnEl.addEventListener('click', () => {
                this.showDatePicker();
            });
        }

        // Add note
        const addNoteBtnEl = document.getElementById('add-note-btn');
        if (addNoteBtnEl) {
            addNoteBtnEl.addEventListener('click', () => {
                this.addNote();
            });
        }

        // Enhanced auto-save for note inputs
        const noteTitleEl = document.getElementById('note-title');
        const noteContentEl = document.getElementById('note-content');
        if (noteTitleEl && noteContentEl) {
            let currentAutoSavedNoteId = null;

            // Save immediately when user starts typing in content
            const handleNoteInput = () => {
                const title = noteTitleEl.value.trim();
                const content = noteContentEl.value.trim();

                if (content && !currentAutoSavedNoteId) {
                    // Create note immediately when user starts typing content
                    currentAutoSavedNoteId = this.createAutoSavedNote(title, content);
                } else if (content && currentAutoSavedNoteId) {
                    // Update existing auto-saved note
                    this.updateAutoSavedNote(currentAutoSavedNoteId, title, content);
                }
            };

            noteTitleEl.addEventListener('input', handleNoteInput);
            noteContentEl.addEventListener('input', handleNoteInput);

            // Handle Enter key in content area
            noteContentEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    const title = noteTitleEl.value.trim();
                    const content = noteContentEl.value.trim();
                    if (content && currentAutoSavedNoteId) {
                        // Finalize the auto-saved note
                        this.finalizeAutoSavedNote(currentAutoSavedNoteId);
                        currentAutoSavedNoteId = null;
                        this.hideAddNote();
                    }
                }
            });

            // Handle ESC key - delete if empty
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    const content = noteContentEl.value.trim();
                    if (!content && currentAutoSavedNoteId) {
                        this.deleteAutoSavedNote(currentAutoSavedNoteId);
                    }
                    currentAutoSavedNoteId = null;
                    this.hideAddNote();
                }
            };

            noteTitleEl.addEventListener('keydown', handleEscape);
            noteContentEl.addEventListener('keydown', handleEscape);

            // Handle blur (clicking outside) - delete if empty
            const handleBlur = () => {
                setTimeout(() => {
                    const content = noteContentEl.value.trim();
                    if (!content && currentAutoSavedNoteId) {
                        this.deleteAutoSavedNote(currentAutoSavedNoteId);
                        currentAutoSavedNoteId = null;
                        this.hideAddNote();
                    }
                }, 100); // Small delay to allow other click events to process
            };

            noteTitleEl.addEventListener('blur', handleBlur);
            noteContentEl.addEventListener('blur', handleBlur);
        }

        const cancelNoteBtnEl = document.getElementById('cancel-note-btn');
        if (cancelNoteBtnEl) {
            cancelNoteBtnEl.addEventListener('click', () => {
                this.hideAddNote();
            });
        }

        // Settings
        const settingsBtnEl = document.getElementById('settings-btn');
        if (settingsBtnEl) {
            settingsBtnEl.addEventListener('click', () => {
                this.openSettings();
            });
        }

        const closeSettingsEl = document.getElementById('close-settings');
        if (closeSettingsEl) {
            closeSettingsEl.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        // Toggle completed todos
        const toggleCompletedEl = document.getElementById('toggle-completed');
        if (toggleCompletedEl) {
            toggleCompletedEl.addEventListener('click', (e) => {
                this.settings.showCompleted = !this.settings.showCompleted;
                e.target.textContent = this.settings.showCompleted ? 'Hide Completed' : 'Show Completed';
                this.updateUI();
                this.saveSettings();
            });
        }

        // Sidebar mode
        const sidebarBtnEl = document.getElementById('sidebar-btn');
        if (sidebarBtnEl) {
            sidebarBtnEl.addEventListener('click', () => {
                this.openSidebar();
            });
        }

        // Settings form
        const sidebarModeEl = document.getElementById('sidebar-mode');
        if (sidebarModeEl) {
            sidebarModeEl.addEventListener('change', (e) => {
                this.settings.sidebarMode = e.target.checked;
                this.saveSettings();

                // Note: User can manually open sidebar using the sidebar button
            });
        }

        const verticalSplitModeEl = document.getElementById('vertical-split-mode');
        if (verticalSplitModeEl) {
            verticalSplitModeEl.addEventListener('change', (e) => {
                this.settings.verticalSplitMode = e.target.checked;
                this.saveSettings();
            });
        }

        const floatingWindowModeEl = document.getElementById('floating-window-mode');
        if (floatingWindowModeEl) {
            floatingWindowModeEl.addEventListener('change', (e) => {
                this.settings.floatingWindowMode = e.target.checked;
                this.saveSettings();
            });
        }

        // Date picker modal
        const closeDatePickerEl = document.getElementById('close-date-picker');
        if (closeDatePickerEl) {
            closeDatePickerEl.addEventListener('click', () => {
                const modalEl = document.getElementById('date-picker-modal');
                if (modalEl) {
                    modalEl.style.display = 'none';
                }
            });
        }

        // Quick date options
        document.querySelectorAll('.quick-date-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const days = e.target.dataset.days;
                const endOfWeek = e.target.dataset.endOfWeek;
                let targetDate;

                if (days) {
                    targetDate = new Date();
                    targetDate.setDate(targetDate.getDate() + parseInt(days));
                } else if (endOfWeek) {
                    targetDate = new Date();
                    const dayOfWeek = targetDate.getDay();
                    const daysUntilFriday = (5 - dayOfWeek + 7) % 7;
                    targetDate.setDate(targetDate.getDate() + (daysUntilFriday === 0 ? 7 : daysUntilFriday));
                }

                if (targetDate) {
                    const dateString = targetDate.toISOString().split('T')[0];
                    const datePickerInput = document.getElementById('date-picker-input');
                    if (datePickerInput) {
                        datePickerInput.value = dateString;
                    }
                }
            });
        });

        const setDateBtnEl = document.getElementById('set-date-btn');
        if (setDateBtnEl) {
            setDateBtnEl.addEventListener('click', () => {
                this.setSelectedDate();
            });
        }

        const clearDateBtnEl = document.getElementById('clear-date-btn');
        if (clearDateBtnEl) {
            clearDateBtnEl.addEventListener('click', () => {
                this.clearSelectedDate();
            });
        }

        // Date picker input - allow Enter key to save
        const datePickerInputEl = document.getElementById('date-picker-input');
        if (datePickerInputEl) {
            datePickerInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.setSelectedDate();
                }
            });
        }

        // Collapsible sections
        document.querySelectorAll('.section-header.collapsible').forEach(header => {
            header.addEventListener('click', (e) => {
                // Don't toggle if clicking on collapse button or new item button
                if (e.target.classList.contains('collapse-btn') ||
                    e.target.classList.contains('new-item-btn')) return;
                this.toggleSection(header.dataset.section);
            });
        });

        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sectionHeader = e.target.closest('.section-header') || e.target.closest('.completed-header');
                if (sectionHeader && sectionHeader.dataset.section) {
                    this.toggleSection(sectionHeader.dataset.section);
                }
            });
        });

        // Undo toast
        const undoBtnEl = document.getElementById('undo-btn');
        if (undoBtnEl) {
            undoBtnEl.addEventListener('click', () => {
                this.undoDelete();
            });
        }

        const closeToastEl = document.getElementById('close-toast');
        if (closeToastEl) {
            closeToastEl.addEventListener('click', () => {
                this.hideUndoToast();
            });
        }

        // Edit note modal
        const closeEditNoteEl = document.getElementById('close-edit-note');
        if (closeEditNoteEl) {
            closeEditNoteEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        const saveNoteBtnEl = document.getElementById('save-note-btn');
        if (saveNoteBtnEl) {
            saveNoteBtnEl.addEventListener('click', () => {
                this.saveEditedNote();
            });
        }

        const cancelEditNoteBtnEl = document.getElementById('cancel-edit-note-btn');
        if (cancelEditNoteBtnEl) {
            cancelEditNoteBtnEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        // Completed/Archived page
        const viewCompletedBtnEl = document.getElementById('view-completed-btn');
        if (viewCompletedBtnEl) {
            viewCompletedBtnEl.addEventListener('click', () => {
                this.showCompletedArchivedPage();
            });
        }

        const backToMainBtnEl = document.getElementById('back-to-main-btn');
        if (backToMainBtnEl) {
            backToMainBtnEl.addEventListener('click', () => {
                this.hideCompletedArchivedPage();
            });
        }

        // Fullscreen mode
        const fullscreenBtnEl = document.getElementById('fullscreen-btn');
        if (fullscreenBtnEl) {
            fullscreenBtnEl.addEventListener('click', () => {
                this.openFullscreen();
            });
        }

        // Floating window mode
        const floatingWindowBtnEl = document.getElementById('floating-window-btn');
        if (floatingWindowBtnEl) {
            floatingWindowBtnEl.addEventListener('click', () => {
                this.openFloatingWindow();
            });
        }

        // Global mode icon toggles (inline in forms)
        const globalModeIconTodoEl = document.getElementById('global-mode-icon-todo');
        const globalModeIconNoteEl = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodoEl) {
            globalModeIconTodoEl.addEventListener('click', () => {
                this.todoGlobalMode = !this.todoGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();
            });
        }

        if (globalModeIconNoteEl) {
            globalModeIconNoteEl.addEventListener('click', () => {
                this.noteGlobalMode = !this.noteGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();
            });
        }

        // Setup drag and drop
        this.setupDragAndDrop();
    }

    setupDragAndDrop() {
        // Add event listeners to the container for drag and drop
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        [todosContainer, notesContainer].forEach(container => {
            if (container) {
                container.addEventListener('dragstart', this.handleDragStart.bind(this));
                container.addEventListener('dragover', this.handleDragOver.bind(this));
                container.addEventListener('drop', this.handleDrop.bind(this));
                container.addEventListener('dragend', this.handleDragEnd.bind(this));
            }
        });
    }

    handleDragStart(e) {
        if (e.target.classList.contains('item')) {
            this.draggedElement = e.target;
            e.target.style.opacity = '0.5';
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', e.target.outerHTML);
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        const afterElement = this.getDragAfterElement(e.currentTarget, e.clientY);
        const dragging = this.draggedElement;

        if (afterElement == null) {
            e.currentTarget.appendChild(dragging);
        } else {
            e.currentTarget.insertBefore(dragging, afterElement);
        }
    }

    handleDrop(e) {
        e.preventDefault();
        if (this.draggedElement) {
            this.reorderItems();
        }
    }

    handleDragEnd(e) {
        if (e.target.classList.contains('item')) {
            e.target.style.opacity = '';
        }
        this.draggedElement = null;
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    reorderItems() {
        // Get the new order from the DOM
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        if (todosContainer) {
            const todoElements = [...todosContainer.querySelectorAll('.item[data-todo-id]')];
            const newTodoOrder = todoElements.map(el => el.dataset.todoId);
            this.reorderTodos(newTodoOrder);
        }

        if (notesContainer) {
            const noteElements = [...notesContainer.querySelectorAll('.item[data-note-id]')];
            const newNoteOrder = noteElements.map(el => el.dataset.noteId);
            this.reorderNotes(newNoteOrder);
        }
    }

    reorderTodos(newOrder) {
        const reorderedTodos = [];
        newOrder.forEach(id => {
            const todo = this.todos.find(t => t.id === id);
            if (todo) reorderedTodos.push(todo);
        });

        // Add any todos not in the new order (shouldn't happen, but safety check)
        this.todos.forEach(todo => {
            if (!newOrder.includes(todo.id)) {
                reorderedTodos.push(todo);
            }
        });

        this.todos = reorderedTodos;
        this.saveData();
    }

    reorderNotes(newOrder) {
        const reorderedNotes = [];
        newOrder.forEach(id => {
            const note = this.notes.find(n => n.id === id);
            if (note) reorderedNotes.push(note);
        });

        // Add any notes not in the new order (shouldn't happen, but safety check)
        this.notes.forEach(note => {
            if (!newOrder.includes(note.id)) {
                reorderedNotes.push(note);
            }
        });

        this.notes = reorderedNotes;
        this.saveData();
    }

    switchScope(scopeName) {
        // Update scope buttons
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const scopeBtn = document.querySelector(`[data-scope="${scopeName}"]`);
        if (scopeBtn) {
            scopeBtn.classList.add('active');
        }

        this.currentScope = scopeName;

        // Show/hide grouping selector and update options based on scope
        const groupingSelector = document.getElementById('grouping-selector');
        const groupBySelect = document.getElementById('group-by');

        if (scopeName === 'all' || scopeName === 'global' || scopeName === 'website' || scopeName === 'current-tab') {
            groupingSelector.style.display = 'flex';

            // Update grouping options based on scope
            if (scopeName === 'website') {
                // Hide website option for website scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'current-tab') {
                // Hide website option for current-tab scope (since it's just one page)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'global') {
                // Hide website option for global scope (since global items aren't tied to websites)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else {
                // Show website option for all scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'block';
                }
            }
        } else {
            groupingSelector.style.display = 'none';
        }

        this.updateUI();
    }

    openSettings() {
        const modalEl = document.getElementById('settings-modal');
        const sidebarModeEl = document.getElementById('sidebar-mode');
        const verticalSplitModeEl = document.getElementById('vertical-split-mode');
        const floatingWindowModeEl = document.getElementById('floating-window-mode');

        if (modalEl) modalEl.style.display = 'flex';
        if (sidebarModeEl) sidebarModeEl.checked = this.settings.sidebarMode;
        if (verticalSplitModeEl) verticalSplitModeEl.checked = this.settings.verticalSplitMode;
        if (floatingWindowModeEl) floatingWindowModeEl.checked = this.settings.floatingWindowMode;
    }

    closeSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    async openSidebar() {
        try {
            // Get the current window first
            const currentWindow = await chrome.windows.getCurrent();
            // Use Chrome's Side Panel API with the actual window ID
            await chrome.sidePanel.open({ windowId: currentWindow.id });
        } catch (error) {
            console.error('Failed to open side panel:', error);
            // Fallback: just show a message to the user
            alert('Please use the Chrome side panel setting to enable sidebar mode. Go to chrome://extensions, find this extension, and click "Details" then enable "Allow in side panel".');
        }
    }



    checkSidebarMode() {
        // Check if we're in sidebar mode based on URL parameters or window characteristics
        const urlParams = new URLSearchParams(window.location.search);
        const isSidebar = urlParams.get('sidebar') === 'true';

        // Check if we're in Chrome's native side panel (narrow width and specific context)
        const isNativeSidePanel = window.location.protocol === 'chrome-extension:' &&
                                  (window.outerWidth < 500 || window.innerWidth < 500);

        // Check if user has sidebar mode enabled in settings
        const userWantsSidebar = this.settings.sidebarMode;

        if (isSidebar || isNativeSidePanel || userWantsSidebar) {
            // Add sidebar-specific styling
            document.body.classList.add('sidebar-mode');

            // Set up auto-refresh for sidebar mode
            this.setupSidebarRefresh();
        } else {
            // Remove sidebar styling if not in sidebar mode
            document.body.classList.remove('sidebar-mode');
        }
    }

    setupSidebarRefresh() {
        // Listen for messages from background script about tab changes
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'tabChanged' || message.action === 'activeTabChanged') {
                // Refresh current tab info and update UI
                this.loadCurrentTab().then(() => {
                    this.updateUI();
                });
            }
        });

        // Listen for tab changes directly (backup method)
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async () => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Listen for tab changes in sidebar mode
        if (!document.body.classList.contains('fullscreen')) {
            this.setupTabChangeListener();
        }
    }

    setupTabChangeListener() {
        // Use Chrome's tab API to listen for tab changes
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async (activeInfo) => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Also listen for tab updates (URL changes within same tab)
        if (chrome.tabs && chrome.tabs.onUpdated) {
            chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
                if (changeInfo.status === 'complete' && tab.active) {
                    await this.loadCurrentTab();
                    this.updateUI();
                }
            });
        }

        // Fallback polling for cases where events don't fire
        setInterval(async () => {
            const newTab = await this.getCurrentTabInfo();
            if (newTab && this.currentTab && newTab.url !== this.currentTab.url) {
                await this.loadCurrentTab();
                this.updateUI();
            }
        }, 1000); // Check every second as fallback
    }

    async getCurrentTabInfo() {
        try {
            return await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
        } catch (error) {
            console.error('Error getting current tab info:', error);
            return null;
        }
    }

    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.todoGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in todo global mode
            baseUrl: this.getScopeBaseUrl(),
            notes: '' // Add notes field for todo-note relationships
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddTodo();
    }

    createAutoSavedTodo(text) {
        const dueDateInput = document.getElementById('todo-due-date');

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.todoGlobalMode ? 'global' : (this.currentTab?.url || 'global'),
            baseUrl: this.getScopeBaseUrl(),
            notes: '',
            autoSaved: true,
            isTemporary: true // Mark as temporary until finalized
        };

        this.todos.unshift(todo);
        this.saveData();
        this.updateUI();

        return todo.id;
    }

    updateAutoSavedTodo(todoId, text) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.text = text;
            this.saveData();
            this.updateUI();
        }
    }

    finalizeAutoSavedTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.isTemporary = false;
            this.saveData();
            this.showNotification('Todo saved', 'success', 1500);
        }
    }

    deleteAutoSavedTodo(todoId) {
        this.todos = this.todos.filter(t => t.id !== todoId);
        this.saveData();
        this.updateUI();
    }

    autoSaveTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        // Check if this todo already exists (to avoid duplicates)
        const existingTodo = this.todos.find(t => t.text === text && !t.completed);
        if (existingTodo) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.todoGlobalMode ? 'global' : (this.currentTab?.url || 'global'),
            baseUrl: this.getScopeBaseUrl(),
            notes: '',
            autoSaved: true // Mark as auto-saved
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();

        // Clear the input after auto-save
        input.value = '';
        if (dueDateInput) dueDateInput.value = '';

        // Show a subtle notification
        this.showNotification('Todo auto-saved', 'success', 2000);
    }
    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!content) return;

        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.noteGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in note global mode
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null // For linking notes to todos
        };

        this.notes.unshift(note); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddNote();
    }

    createAutoSavedNote(title, content) {
        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.noteGlobalMode ? 'global' : (this.currentTab?.url || 'global'),
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null,
            autoSaved: true,
            isTemporary: true // Mark as temporary until finalized
        };

        this.notes.unshift(note);
        this.saveData();
        this.updateUI();

        return note.id;
    }

    updateAutoSavedNote(noteId, title, content) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            note.title = title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : '');
            note.content = content;
            this.saveData();
            this.updateUI();
        }
    }

    finalizeAutoSavedNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            note.isTemporary = false;
            this.saveData();
            this.showNotification('Note saved', 'success', 1500);
        }
    }

    deleteAutoSavedNote(noteId) {
        this.notes = this.notes.filter(n => n.id !== noteId);
        this.saveData();
        this.updateUI();
    }
    
    getScopeUrl() {
        // Always save full URL, but scope determines what we show
        return this.currentTab?.url || '';
    }

    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }

    getFilteredTodos() {
        const allTodos = this.todos;

        switch (this.currentScope) {
            case 'global':
                return allTodos.filter(todo => todo.url === 'global');
            case 'current-tab':
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allTodos.filter(todo => {
                    try {
                        if (todo.url === 'global') return false; // Exclude global todos from website view
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allTodos.filter(todo => todo.url !== 'global'); // Exclude global todos from all view
            default:
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
        }
    }

    getFilteredNotes() {
        const allNotes = this.notes.filter(note => !note.archived);

        switch (this.currentScope) {
            case 'global':
                return allNotes.filter(note => note.url === 'global');
            case 'current-tab':
                return allNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allNotes.filter(note => {
                    try {
                        if (note.url === 'global') return false; // Exclude global notes from website view
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allNotes.filter(note => note.url !== 'global'); // Exclude global notes from all view
            default:
                return allNotes.filter(note => note.url === this.currentTab?.url);
        }
    }

    updateUI() {
        // Skip regular UI updates in fullscreen mode
        if (document.body.classList.contains('fullscreen')) {
            return;
        }

        this.updateTodosList();
        this.updateNotesList();
        this.updateCounts();

        // Update placeholder text based on current scope and per-item global mode
        this.updatePlaceholders();

        this.updateGlobalModeDisplay();
    }

    updatePlaceholders() {
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        // Update todo placeholder based on per-item global mode
        if (todoInput) {
            if (this.todoGlobalMode) {
                todoInput.placeholder = 'Add a global todo (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        todoInput.placeholder = 'Add a new todo for this page...';
                        break;
                    case 'website':
                        todoInput.placeholder = 'Add a new todo for this website...';
                        break;
                    case 'all':
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                    default:
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                }
            }
        }

        // Update note placeholder based on per-item global mode
        if (noteContent) {
            if (this.noteGlobalMode) {
                noteContent.placeholder = 'Add a global note (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        noteContent.placeholder = 'Add a new note for this page...';
                        break;
                    case 'website':
                        noteContent.placeholder = 'Add a new note for this website...';
                        break;
                    case 'all':
                        noteContent.placeholder = 'Add a new note...';
                        break;
                    default:
                        noteContent.placeholder = 'Add a new note...';
                        break;
                }
            }
        }
    }

    updateGlobalModeDisplay() {
        this.updateGlobalModeIcons();

        // Disable scope selector when in global mode
        const scopeSelector = document.getElementById('scope-selector');
        if (scopeSelector) {
            scopeSelector.disabled = this.globalMode;
            scopeSelector.style.opacity = this.globalMode ? '0.5' : '1';
        }
    }

    updateGlobalModeIcons() {
        const globalModeIconTodo = document.getElementById('global-mode-icon-todo');
        const globalModeIconNote = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodo) {
            if (this.todoGlobalMode) {
                globalModeIconTodo.classList.add('active');
            } else {
                globalModeIconTodo.classList.remove('active');
            }
        }

        if (globalModeIconNote) {
            if (this.noteGlobalMode) {
                globalModeIconNote.classList.add('active');
            } else {
                globalModeIconNote.classList.remove('active');
            }
        }
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'All Items': items };
        }

        const groups = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'website':
                    try {
                        const url = new URL(item.url);
                        // Extract base URL (hostname + path without query/hash)
                        groupKey = url.hostname + (url.pathname !== '/' ? url.pathname : '');
                    } catch (error) {
                        groupKey = 'Global';
                    }
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        const date = new Date(item.dueDate);
                        groupKey = date.toDateString();
                    } else {
                        groupKey = 'No Due Date';
                    }
                    break;
                case 'created-date':
                    const createdDate = new Date(item.createdAt);
                    groupKey = createdDate.toDateString();
                    break;
                default:
                    groupKey = 'All Items';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
        });

        // Sort groups for due-date: items with due dates first, then no due date
        if (groupBy === 'due-date') {
            const sortedGroups = {};
            const withDueDates = [];
            const withoutDueDates = [];

            Object.keys(groups).forEach(key => {
                if (key === 'No Due Date') {
                    withoutDueDates.push(key);
                } else {
                    withDueDates.push(key);
                }
            });

            // Sort dates chronologically
            withDueDates.sort((a, b) => new Date(a) - new Date(b));

            // Add with due dates first, then without
            [...withDueDates, ...withoutDueDates].forEach(key => {
                sortedGroups[key] = groups[key];
            });

            return sortedGroups;
        }

        return groups;
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const filteredTodos = this.getFilteredTodos().filter(todo => !todo.completed);

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '';
            return;
        }

        todosList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                todosList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredTodos.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });
        }
    }



    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '';
            return;
        }

        notesList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                notesList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredNotes.forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });
        }
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''} ${todo.isTemporary ? 'temporary' : ''}`;
        todoEl.draggable = true;
        todoEl.dataset.todoId = todo.id;
        todoEl.dataset.itemType = 'todo';

        const dueDateDisplay = this.formatDueDate(todo.dueDate);
        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';

        const faviconUrl = todo.url !== 'global' ? this.getFaviconUrl(todo.url) : null;

        todoEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
            <div class="item-content">
                ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">
                    ${faviconUrl ? `<img src="${faviconUrl}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                </a>` : ''}
                <div class="item-text" data-todo-id="${todo.id}" style="cursor: pointer;">${todo.url === 'global' ? '🌐 ' : ''}${todo.text}</div>
                <div class="item-meta">
                    ${dueDateDisplay ? `<span class="item-due-date ${this.getDueDateClass(todo.dueDate)}">${dueDateDisplay}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-btn" data-todo-id="${todo.id}" title="Edit">✏️</button>
                <button class="date-btn" data-todo-id="${todo.id}" title="Set Due Date">📅</button>
                <button class="delete-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const checkbox = todoEl.querySelector('.item-checkbox');
        checkbox.addEventListener('change', () => this.toggleTodo(todo.id));

        const textEl = todoEl.querySelector('.item-text');
        textEl.addEventListener('click', () => this.editTodo(todo.id));

        const editBtn = todoEl.querySelector('.edit-btn');
        editBtn.addEventListener('click', () => this.editTodo(todo.id));

        const dateBtn = todoEl.querySelector('.date-btn');
        dateBtn.addEventListener('click', () => this.setDueDate(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = `item ${note.isTemporary ? 'temporary' : ''}`;
        noteEl.draggable = true;
        noteEl.dataset.noteId = note.id;
        noteEl.dataset.itemType = 'note';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';

        const createdDate = new Date(note.createdAt).toLocaleDateString();

        noteEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">
                        ${this.getFaviconUrl(note.url) ? `<img src="${this.getFaviconUrl(note.url)}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}
                    ${note.title ? `<strong>${note.url === 'global' ? '🌐 ' : ''}${note.title}</strong> <span class="created-date">${createdDate}</span><br>` : ''}
                    ${note.url === 'global' && !note.title ? '🌐 ' : ''}${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                <button class="archive-note-btn" data-note-id="${note.id}" title="Archive">📁</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const editBtn = noteEl.querySelector('.edit-note-btn');
        editBtn.addEventListener('click', () => this.editNote(note.id));

        const archiveBtn = noteEl.querySelector('.archive-note-btn');
        archiveBtn.addEventListener('click', () => this.archiveNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }



    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    getFaviconUrl(url) {
        try {
            const urlObj = new URL(url);
            return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`;
        } catch (error) {
            return null;
        }
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else {
            return due.toLocaleDateString();
        }
    }

    getDueDateClass(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'tomorrow';
        } else {
            return '';
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveData();
            this.updateUI(); // Update both active and completed lists
        }
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const newText = prompt('Edit todo:', todo.text);
            if (newText !== null && newText.trim()) {
                todo.text = newText.trim();
                this.saveData();
                this.updateUI();
            }
        }
    }

    setDueDate(todoId) {
        this.currentEditingTodoId = todoId;
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const currentDate = todo.dueDate || '';
            document.getElementById('date-picker-input').value = currentDate;
            document.getElementById('date-picker-modal').style.display = 'flex';
        }
    }

    deleteTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Todo deleted', () => {
                this.todos.push(todoBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            this.currentEditingNoteId = noteId;

            const titleEl = document.getElementById('edit-note-title');
            const contentEl = document.getElementById('edit-note-content');
            const modalEl = document.getElementById('edit-note-modal');

            if (titleEl) titleEl.value = note.title;
            if (contentEl) contentEl.value = note.content;
            if (modalEl) modalEl.style.display = 'flex';
        }
    }

    closeEditNoteModal() {
        const modalEl = document.getElementById('edit-note-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
        this.currentEditingNoteId = null;
    }

    saveEditedNote() {
        if (!this.currentEditingNoteId) return;

        const note = this.notes.find(n => n.id === this.currentEditingNoteId);
        if (!note) return;

        const titleEl = document.getElementById('edit-note-title');
        const contentEl = document.getElementById('edit-note-content');

        const newTitle = titleEl ? titleEl.value.trim() : '';
        const newContent = contentEl ? contentEl.value.trim() : '';

        if (newContent) {
            note.title = newTitle || newContent.split('\n')[0].substring(0, 50);
            note.content = newContent;
            this.saveData();
            this.updateUI();
        }

        this.closeEditNoteModal();
    }

    linkToTodo(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const availableTodos = this.getFilteredTodos();
            if (availableTodos.length === 0) {
                alert('No todos available to link to. Create a todo first.');
                return;
            }

            const todoOptions = availableTodos.map(todo => `${todo.id}: ${todo.text}`).join('\n');
            const selectedTodoId = prompt(`Link to which todo?\n\n${todoOptions}\n\nEnter todo ID:`);

            if (selectedTodoId && availableTodos.find(t => t.id === selectedTodoId)) {
                note.linkedTodoId = selectedTodoId;
                this.saveData();
                this.updateUI();
                alert('Note linked to todo successfully!');
            }
        }
    }

    archiveNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            note.archived = true;
            note.archivedAt = new Date().toISOString();
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note archived', () => {
                const restoredNote = this.notes.find(n => n.id === noteId);
                if (restoredNote) {
                    restoredNote.archived = false;
                    delete restoredNote.archivedAt;
                    this.saveData();
                    this.updateUI();
                }
            });
        }
    }

    deleteNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note deleted', () => {
                this.notes.push(noteBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    saveSettings() {
        chrome.storage.local.set({
            settings: this.settings
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // New UI methods
    showAddTodo() {
        // Ensure todos section is expanded
        const todosSection = document.querySelector('.todos-section');
        const todosContent = todosSection.querySelector('.section-content');
        if (todosContent) {
            todosContent.style.display = 'block';
            todosSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-todo-section').style.display = 'block';
        const todoInput = document.getElementById('todo-input');
        todoInput.focus();

        // Scroll to the form
        document.getElementById('add-todo-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddTodo() {
        document.getElementById('add-todo-section').style.display = 'none';
        document.getElementById('todo-input').value = '';
        document.getElementById('todo-due-date').style.display = 'none';
        document.getElementById('todo-due-date').value = '';

        // Reset per-item global mode
        this.todoGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    showAddNote() {
        // Ensure notes section is expanded
        const notesSection = document.querySelector('.notes-section');
        const notesContent = notesSection.querySelector('.section-content');
        if (notesContent) {
            notesContent.style.display = 'block';
            notesSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-note-section').style.display = 'block';
        const noteTitle = document.getElementById('note-title');
        noteTitle.focus();

        // Scroll to the form
        document.getElementById('add-note-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddNote() {
        document.getElementById('add-note-section').style.display = 'none';
        document.getElementById('note-title').value = '';
        document.getElementById('note-content').value = '';

        // Reset per-item global mode
        this.noteGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    showDatePicker() {
        const todoDueDateEl = document.getElementById('todo-due-date');
        const datePickerInputEl = document.getElementById('date-picker-input');
        const modalEl = document.getElementById('date-picker-modal');

        const currentDate = todoDueDateEl ? todoDueDateEl.value : '';
        if (datePickerInputEl) {
            datePickerInputEl.value = currentDate;
        }
        if (modalEl) {
            modalEl.style.display = 'flex';
        }
    }

    setSelectedDate() {
        const datePickerInput = document.getElementById('date-picker-input');
        const selectedDate = datePickerInput ? datePickerInput.value : '';

        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = selectedDate || null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');

            if (todoDueDateEl) {
                todoDueDateEl.value = selectedDate;
                todoDueDateEl.style.display = selectedDate ? 'inline-block' : 'none';
            }

            // Ensure the Add button remains visible
            if (addTodoBtn) {
                addTodoBtn.style.display = 'inline-block';
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    clearSelectedDate() {
        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');
            if (todoDueDateEl) {
                todoDueDateEl.value = '';
                todoDueDateEl.style.display = 'none';
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    toggleSection(sectionName) {
        const content = document.getElementById(`${sectionName}-content`);
        const btn = document.querySelector(`[data-section="${sectionName}"] .collapse-btn`);

        if (!content || !btn) return;

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            btn.classList.remove('collapsed');
            btn.textContent = '▼';
        } else {
            content.classList.add('collapsed');
            btn.classList.add('collapsed');
            btn.textContent = '▶';
        }
    }



    showUndoToast(message, undoCallback) {
        this.undoCallback = undoCallback;
        const messageEl = document.getElementById('undo-message');
        const toastEl = document.getElementById('undo-toast');
        const progressBarEl = document.getElementById('toast-progress-bar');

        if (messageEl) messageEl.textContent = message;
        if (toastEl) toastEl.style.display = 'block';

        // Reset progress bar
        if (progressBarEl) {
            progressBarEl.style.width = '100%';
        }

        // Clear any existing timeout
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start progress bar animation
        let timeLeft = 20000; // 20 seconds
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            timeLeft -= updateInterval;
            const percentage = (timeLeft / 20000) * 100;

            if (progressBarEl) {
                progressBarEl.style.width = `${Math.max(0, percentage)}%`;
            }

            if (timeLeft <= 0) {
                this.hideUndoToast();
            }
        }, updateInterval);

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    hideUndoToast() {
        const toastEl = document.getElementById('undo-toast');
        if (toastEl) {
            toastEl.style.display = 'none';
        }

        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
            this.undoTimeout = null;
        }

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        this.undoCallback = null;
    }

    undoDelete() {
        if (this.undoCallback) {
            this.undoCallback();
            this.hideUndoToast();
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification-toast');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification-toast';
            notification.className = 'notification-toast';
            document.body.appendChild(notification);
        }

        notification.textContent = message;
        notification.className = `notification-toast ${type}`;
        notification.style.display = 'block';

        // Auto-hide after duration
        setTimeout(() => {
            notification.style.display = 'none';
        }, duration);
    }

    updateCounts() {
        const filteredTodos = this.getFilteredTodos();
        const completedTodos = filteredTodos.filter(todo => todo.completed);
        const activeTodos = filteredTodos.filter(todo => !todo.completed);
        const filteredNotes = this.getFilteredNotes();

        document.getElementById('todos-count').textContent = `(${activeTodos.length})`;
        document.getElementById('notes-count').textContent = `(${filteredNotes.length})`;
    }

    showCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'none';
        if (completedPage) completedPage.style.display = 'block';

        this.updateCompletedArchivedPage();
    }

    hideCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'block';
        if (completedPage) completedPage.style.display = 'none';
    }

    updateCompletedArchivedPage() {
        // Update completed todos
        const completedTodos = this.todos.filter(todo => todo.completed);
        const completedPageList = document.getElementById('completed-page-list');
        const completedPageCount = document.getElementById('completed-page-count');

        if (completedPageList) {
            completedPageList.innerHTML = '';
            completedTodos.forEach(todo => {
                const todoEl = this.createCompletedTodoElement(todo);
                completedPageList.appendChild(todoEl);
            });
        }

        if (completedPageCount) {
            completedPageCount.textContent = `(${completedTodos.length})`;
        }

        // Update archived notes
        const archivedNotes = this.notes.filter(note => note.archived);
        const archivedPageList = document.getElementById('archived-page-list');
        const archivedPageCount = document.getElementById('archived-page-count');

        if (archivedPageList) {
            archivedPageList.innerHTML = '';
            archivedNotes.forEach(note => {
                const noteEl = this.createArchivedNoteElement(note);
                archivedPageList.appendChild(noteEl);
            });
        }

        if (archivedPageCount) {
            archivedPageCount.textContent = `(${archivedNotes.length})`;
        }
    }

    createCompletedTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = 'item completed';

        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';
        const completedDate = todo.completedAt ? new Date(todo.completedAt).toLocaleDateString() : '';

        todoEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">🔗</a> ` : ''}
                    <span class="todo-text">${todo.text}</span>
                    ${completedDate ? `<span class="completed-date"> - Completed ${completedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-todo-btn" data-todo-id="${todo.id}" title="Restore">↩️</button>
                <button class="delete-todo-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = todoEl.querySelector('.restore-todo-btn');
        restoreBtn.addEventListener('click', () => this.restoreTodo(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-todo-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createArchivedNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item archived';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';
        const archivedDate = note.archivedAt ? new Date(note.archivedAt).toLocaleDateString() : '';

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">🔗</a> ` : ''}
                    ${note.title ? `<strong>${note.title}</strong><br>` : ''}
                    ${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                    ${archivedDate ? `<span class="archived-date"> - Archived ${archivedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-note-btn" data-note-id="${note.id}" title="Restore">↩️</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = noteEl.querySelector('.restore-note-btn');
        restoreBtn.addEventListener('click', () => this.restoreNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }

    restoreTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = false;
            delete todo.completedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    restoreNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            note.archived = false;
            delete note.archivedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    openFullscreen() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('fullscreen.html')
        });
    }

    openFloatingWindow() {
        chrome.windows.create({
            url: chrome.runtime.getURL('popup.html'),
            type: 'popup',
            width: 400,
            height: 600,
            focused: true
        });
    }

    initFullscreenMode() {
        // Check if we're in fullscreen mode
        if (!document.body.classList.contains('fullscreen')) {
            return;
        }

        // Set up fullscreen-specific event listeners
        const addGlobalTodoBtnEl = document.getElementById('add-global-todo-btn');
        if (addGlobalTodoBtnEl) {
            addGlobalTodoBtnEl.addEventListener('click', () => {
                this.addGlobalTodo();
            });
        }

        const addGlobalNoteBtnEl = document.getElementById('add-global-note-btn');
        if (addGlobalNoteBtnEl) {
            addGlobalNoteBtnEl.addEventListener('click', () => {
                this.addGlobalNote();
            });
        }

        // Load and display global items
        this.loadData().then(() => {
            // Apply vertical split layout if enabled
            if (this.settings.verticalSplitMode) {
                document.body.classList.add('vertical-split');
            }

            this.updateGlobalTodosList();
            this.updateGlobalNotesList();
        });
    }

    addGlobalTodo() {
        const textEl = document.getElementById('global-todo-text');
        const dateEl = document.getElementById('global-todo-date');
        const urlEl = document.getElementById('global-todo-url');

        const text = textEl ? textEl.value.trim() : '';
        const dueDate = dateEl ? dateEl.value : '';
        const url = urlEl ? urlEl.value.trim() : '';

        if (text) {
            const todo = {
                id: Date.now().toString(),
                text: text,
                completed: false,
                url: url || 'global',
                createdAt: new Date().toISOString(),
                dueDate: dueDate || null
            };

            this.todos.unshift(todo);
            this.saveData();
            this.updateGlobalTodosList();

            // Clear form
            if (textEl) textEl.value = '';
            if (dateEl) dateEl.value = '';
            if (urlEl) urlEl.value = '';
        }
    }

    addGlobalNote() {
        const titleEl = document.getElementById('global-note-title');
        const contentEl = document.getElementById('global-note-content');
        const urlEl = document.getElementById('global-note-url');

        const title = titleEl ? titleEl.value.trim() : '';
        const content = contentEl ? contentEl.value.trim() : '';
        const url = urlEl ? urlEl.value.trim() : '';

        if (content) {
            const note = {
                id: Date.now().toString(),
                title: title || content.split('\n')[0].substring(0, 50),
                content: content,
                url: url || 'global',
                createdAt: new Date().toISOString()
            };

            this.notes.unshift(note);
            this.saveData();
            this.updateGlobalNotesList();

            // Clear form
            if (titleEl) titleEl.value = '';
            if (contentEl) contentEl.value = '';
            if (urlEl) urlEl.value = '';
        }
    }

    updateGlobalTodosList() {
        const listEl = document.getElementById('global-todos-list');
        if (!listEl) return;

        const globalTodos = this.todos.filter(todo => todo.url === 'global' && !todo.completed);
        listEl.innerHTML = '';

        globalTodos.forEach(todo => {
            const todoEl = this.createTodoElement(todo);
            listEl.appendChild(todoEl);
        });
    }

    updateGlobalNotesList() {
        const listEl = document.getElementById('global-notes-list');
        if (!listEl) return;

        const globalNotes = this.notes.filter(note => note.url === 'global' && !note.archived);
        listEl.innerHTML = '';

        globalNotes.forEach(note => {
            const noteEl = this.createNoteElement(note);
            listEl.appendChild(noteEl);
        });
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
    // Make app globally available for inline event handlers
    window.app = app;
});
