// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'global';
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        
        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();
        this.updateUI();
    }
    
    async loadCurrentTab() {
        try {
            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
            
            if (this.currentTab) {
                document.getElementById('current-url').textContent = 
                    this.formatUrl(this.currentTab.url);
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache'], resolve);
            });
            
            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // Scope selection
        document.getElementById('scope-select').addEventListener('change', (e) => {
            this.currentScope = e.target.value;
            this.updateUI();
        });
        
        // Add todo
        document.getElementById('add-todo-btn').addEventListener('click', () => {
            this.addTodo();
        });
        
        document.getElementById('todo-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTodo();
            }
        });
        
        // Add note
        document.getElementById('add-note-btn').addEventListener('click', () => {
            this.addNote();
        });
        
        // Grouping changes
        document.getElementById('todo-group-by').addEventListener('change', () => {
            this.updateTodosList();
        });
        
        document.getElementById('note-group-by').addEventListener('change', () => {
            this.updateNotesList();
        });
    }
    
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }
    
    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();
        
        if (!text) return;
        
        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.getScopeUrl(),
            baseUrl: this.getScopeBaseUrl(),
            scope: this.currentScope
        };
        
        this.todos.push(todo);
        this.saveData();
        
        input.value = '';
        dueDateInput.value = '';
        this.updateTodosList();
    }
    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();
        
        if (!content) return;
        
        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.getScopeUrl(),
            baseUrl: this.getScopeBaseUrl(),
            scope: this.currentScope
        };
        
        this.notes.push(note);
        this.saveData();
        
        titleInput.value = '';
        contentInput.value = '';
        this.updateNotesList();
    }
    
    getScopeUrl() {
        switch (this.currentScope) {
            case 'current-tab':
                return this.currentTab?.url || '';
            case 'base-url':
                return this.currentTab?.baseUrl || '';
            default:
                return 'global';
        }
    }
    
    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }
    
    getFilteredTodos() {
        switch (this.currentScope) {
            case 'current-tab':
                return this.todos.filter(todo => 
                    todo.url === this.currentTab?.url || 
                    (todo.scope === 'current-tab' && todo.url === this.currentTab?.url)
                );
            case 'base-url':
                return this.todos.filter(todo => {
                    if (!this.currentTab?.baseUrl) return false;
                    return todo.baseUrl === this.currentTab.baseUrl || 
                           todo.url?.startsWith(this.currentTab.baseUrl);
                });
            default:
                return this.todos.filter(todo => todo.scope === 'global' || todo.url === 'global');
        }
    }
    
    getFilteredNotes() {
        switch (this.currentScope) {
            case 'current-tab':
                return this.notes.filter(note =>
                    note.url === this.currentTab?.url ||
                    (note.scope === 'current-tab' && note.url === this.currentTab?.url)
                );
            case 'base-url':
                return this.notes.filter(note => {
                    if (!this.currentTab?.baseUrl) return false;
                    return note.baseUrl === this.currentTab.baseUrl ||
                           note.url?.startsWith(this.currentTab.baseUrl);
                });
            default:
                return this.notes.filter(note => note.scope === 'global' || note.url === 'global');
        }
    }

    updateUI() {
        this.updateTodosList();
        this.updateNotesList();
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const groupBy = document.getElementById('todo-group-by').value;
        const filteredTodos = this.getFilteredTodos();

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '<div class="empty-state"><p>No todos found</p><p>Add your first todo above!</p></div>';
            return;
        }

        const groupedTodos = this.groupItems(filteredTodos, groupBy);
        todosList.innerHTML = '';

        Object.keys(groupedTodos).forEach(groupKey => {
            if (groupBy !== 'none') {
                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.textContent = this.formatGroupHeader(groupKey, groupBy);
                todosList.appendChild(groupHeader);
            }

            groupedTodos[groupKey].forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });
        });
    }

    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const groupBy = document.getElementById('note-group-by').value;
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '<div class="empty-state"><p>No notes found</p><p>Add your first note above!</p></div>';
            return;
        }

        const groupedNotes = this.groupItems(filteredNotes, groupBy);
        notesList.innerHTML = '';

        Object.keys(groupedNotes).forEach(groupKey => {
            if (groupBy !== 'none') {
                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.textContent = this.formatGroupHeader(groupKey, groupBy);
                notesList.appendChild(groupHeader);
            }

            groupedNotes[groupKey].forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });
        });
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''}`;

        const dueDate = todo.dueDate ? new Date(todo.dueDate).toLocaleDateString() : '';
        const createdDate = new Date(todo.createdAt).toLocaleDateString();

        todoEl.innerHTML = `
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''}
                   onchange="app.toggleTodo('${todo.id}')">
            <div class="item-content">
                <div>${todo.text}</div>
                <div class="item-meta">
                    ${dueDate ? `Due: ${dueDate} | ` : ''}Created: ${createdDate}
                    ${this.currentScope === 'base-url' ? ` | ${this.formatUrl(todo.url)}` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button onclick="app.editTodo('${todo.id}')" title="Edit">✏️</button>
                <button onclick="app.deleteTodo('${todo.id}')" title="Delete">🗑️</button>
            </div>
        `;

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item';

        const createdDate = new Date(note.createdAt).toLocaleDateString();

        noteEl.innerHTML = `
            <div class="item-content">
                <div><strong>${note.title}</strong></div>
                <div>${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}</div>
                <div class="item-meta">
                    Created: ${createdDate}
                    ${this.currentScope === 'base-url' ? ` | ${this.formatUrl(note.url)}` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button onclick="app.editNote('${note.id}')" title="Edit">✏️</button>
                <button onclick="app.deleteNote('${note.id}')" title="Delete">🗑️</button>
            </div>
        `;

        return noteEl;
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'all': items };
        }

        const grouped = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'url':
                    groupKey = this.formatUrl(item.url);
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        groupKey = new Date(item.dueDate).toDateString();
                    } else {
                        groupKey = 'No due date';
                    }
                    break;
                case 'created-date':
                    groupKey = new Date(item.createdAt).toDateString();
                    break;
                default:
                    groupKey = 'Other';
            }

            if (!grouped[groupKey]) {
                grouped[groupKey] = [];
            }
            grouped[groupKey].push(item);
        });

        return grouped;
    }

    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveData();
            this.updateTodosList();
        }
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const newText = prompt('Edit todo:', todo.text);
            if (newText !== null && newText.trim()) {
                todo.text = newText.trim();
                this.saveData();
                this.updateTodosList();
            }
        }
    }

    deleteTodo(todoId) {
        if (confirm('Delete this todo?')) {
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateTodosList();
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const newTitle = prompt('Edit note title:', note.title);
            if (newTitle !== null) {
                const newContent = prompt('Edit note content:', note.content);
                if (newContent !== null && newContent.trim()) {
                    note.title = newTitle.trim() || newContent.split('\n')[0].substring(0, 50);
                    note.content = newContent.trim();
                    this.saveData();
                    this.updateNotesList();
                }
            }
        }
    }

    deleteNote(noteId) {
        if (confirm('Delete this note?')) {
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateNotesList();
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
});

// Make app globally available for inline event handlers
window.app = app;
