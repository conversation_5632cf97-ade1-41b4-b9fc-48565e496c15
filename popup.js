// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'all'; // Default to all view
        this.currentGrouping = 'none';
        this.globalMode = false;
        this.currentEditingTodoId = null;
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        this.settings = {
            sidebarMode: false,
            showCompleted: true
        };

        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();
        this.checkSidebarMode();
        this.updateUI();

        // Ensure counts are updated after everything is loaded
        setTimeout(() => {
            this.updateCounts();
        }, 100);
    }
    
    async loadCurrentTab() {
        try {
            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
            
            if (this.currentTab) {
                document.getElementById('current-url').textContent = 
                    this.formatUrl(this.currentTab.url);
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache', 'settings'], resolve);
            });

            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
            this.settings = { ...this.settings, ...(result.settings || {}) };
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Scope selection
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchScope(e.target.dataset.scope);
            });
        });

        // Grouping selection
        const groupByEl = document.getElementById('group-by');
        if (groupByEl) {
            groupByEl.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;
                this.updateUI();
            });
        }

        // New item buttons
        const newTodoBtnEl = document.getElementById('new-todo-btn');
        if (newTodoBtnEl) {
            newTodoBtnEl.addEventListener('click', () => {
                this.showAddTodo();
            });
        }

        const newNoteBtnEl = document.getElementById('new-note-btn');
        if (newNoteBtnEl) {
            newNoteBtnEl.addEventListener('click', () => {
                this.showAddNote();
            });
        }

        // Add todo
        const addTodoBtnEl = document.getElementById('add-todo-btn');
        if (addTodoBtnEl) {
            addTodoBtnEl.addEventListener('click', () => {
                this.addTodo();
            });
        }

        const cancelTodoBtnEl = document.getElementById('cancel-todo-btn');
        if (cancelTodoBtnEl) {
            cancelTodoBtnEl.addEventListener('click', () => {
                this.hideAddTodo();
            });
        }

        const todoInputEl = document.getElementById('todo-input');
        if (todoInputEl) {
            todoInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addTodo();
                }
            });
        }

        // Calendar button
        const calendarBtnEl = document.getElementById('calendar-btn');
        if (calendarBtnEl) {
            calendarBtnEl.addEventListener('click', () => {
                this.showDatePicker();
            });
        }

        // Add note
        const addNoteBtnEl = document.getElementById('add-note-btn');
        if (addNoteBtnEl) {
            addNoteBtnEl.addEventListener('click', () => {
                this.addNote();
            });
        }

        const cancelNoteBtnEl = document.getElementById('cancel-note-btn');
        if (cancelNoteBtnEl) {
            cancelNoteBtnEl.addEventListener('click', () => {
                this.hideAddNote();
            });
        }

        // Settings
        const settingsBtnEl = document.getElementById('settings-btn');
        if (settingsBtnEl) {
            settingsBtnEl.addEventListener('click', () => {
                this.openSettings();
            });
        }

        const closeSettingsEl = document.getElementById('close-settings');
        if (closeSettingsEl) {
            closeSettingsEl.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        // Toggle completed todos
        const toggleCompletedEl = document.getElementById('toggle-completed');
        if (toggleCompletedEl) {
            toggleCompletedEl.addEventListener('click', (e) => {
                this.settings.showCompleted = !this.settings.showCompleted;
                e.target.textContent = this.settings.showCompleted ? 'Hide Completed' : 'Show Completed';
                this.updateUI();
                this.saveSettings();
            });
        }

        // Sidebar mode
        const sidebarBtnEl = document.getElementById('sidebar-btn');
        if (sidebarBtnEl) {
            sidebarBtnEl.addEventListener('click', () => {
                this.openSidebar();
            });
        }

        // Settings form
        const sidebarModeEl = document.getElementById('sidebar-mode');
        if (sidebarModeEl) {
            sidebarModeEl.addEventListener('change', (e) => {
                this.settings.sidebarMode = e.target.checked;
                this.saveSettings();

                // If sidebar mode is enabled, automatically open sidebar
                if (e.target.checked) {
                    this.openSidebar();
                }
            });
        }

        // Date picker modal
        const closeDatePickerEl = document.getElementById('close-date-picker');
        if (closeDatePickerEl) {
            closeDatePickerEl.addEventListener('click', () => {
                const modalEl = document.getElementById('date-picker-modal');
                if (modalEl) {
                    modalEl.style.display = 'none';
                }
            });
        }

        const setDateBtnEl = document.getElementById('set-date-btn');
        if (setDateBtnEl) {
            setDateBtnEl.addEventListener('click', () => {
                this.setSelectedDate();
            });
        }

        const clearDateBtnEl = document.getElementById('clear-date-btn');
        if (clearDateBtnEl) {
            clearDateBtnEl.addEventListener('click', () => {
                this.clearSelectedDate();
            });
        }

        // Collapsible sections
        document.querySelectorAll('.section-header.collapsible').forEach(header => {
            header.addEventListener('click', (e) => {
                if (e.target.classList.contains('collapse-btn')) return;
                this.toggleSection(header.dataset.section);
            });
        });

        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sectionHeader = e.target.closest('.section-header') || e.target.closest('.completed-header');
                if (sectionHeader && sectionHeader.dataset.section) {
                    this.toggleSection(sectionHeader.dataset.section);
                }
            });
        });

        // Undo toast
        const undoBtnEl = document.getElementById('undo-btn');
        if (undoBtnEl) {
            undoBtnEl.addEventListener('click', () => {
                this.undoDelete();
            });
        }

        const closeToastEl = document.getElementById('close-toast');
        if (closeToastEl) {
            closeToastEl.addEventListener('click', () => {
                this.hideUndoToast();
            });
        }

        // Edit note modal
        const closeEditNoteEl = document.getElementById('close-edit-note');
        if (closeEditNoteEl) {
            closeEditNoteEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        const saveNoteBtnEl = document.getElementById('save-note-btn');
        if (saveNoteBtnEl) {
            saveNoteBtnEl.addEventListener('click', () => {
                this.saveEditedNote();
            });
        }

        const cancelEditNoteBtnEl = document.getElementById('cancel-edit-note-btn');
        if (cancelEditNoteBtnEl) {
            cancelEditNoteBtnEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        // Completed/Archived page
        const viewCompletedBtnEl = document.getElementById('view-completed-btn');
        if (viewCompletedBtnEl) {
            viewCompletedBtnEl.addEventListener('click', () => {
                this.showCompletedArchivedPage();
            });
        }

        const backToMainBtnEl = document.getElementById('back-to-main-btn');
        if (backToMainBtnEl) {
            backToMainBtnEl.addEventListener('click', () => {
                this.hideCompletedArchivedPage();
            });
        }

        // Fullscreen mode
        const fullscreenBtnEl = document.getElementById('fullscreen-btn');
        if (fullscreenBtnEl) {
            fullscreenBtnEl.addEventListener('click', () => {
                this.openFullscreen();
            });
        }

        // Global mode toggle
        const globalModeToggleEl = document.getElementById('global-mode-toggle');
        if (globalModeToggleEl) {
            globalModeToggleEl.addEventListener('change', (e) => {
                this.globalMode = e.target.checked;
                this.updateUI();
            });
        }
    }
    
    switchScope(scopeName) {
        // Update scope buttons
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const scopeBtn = document.querySelector(`[data-scope="${scopeName}"]`);
        if (scopeBtn) {
            scopeBtn.classList.add('active');
        }

        this.currentScope = scopeName;

        // Show/hide grouping selector based on scope
        const groupingSelector = document.getElementById('grouping-selector');
        if (scopeName === 'all') {
            groupingSelector.style.display = 'flex';
        } else {
            groupingSelector.style.display = 'none';
        }

        this.updateUI();
    }

    openSettings() {
        const modalEl = document.getElementById('settings-modal');
        const sidebarModeEl = document.getElementById('sidebar-mode');

        if (modalEl) modalEl.style.display = 'flex';
        if (sidebarModeEl) sidebarModeEl.checked = this.settings.sidebarMode;
    }

    closeSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    openSidebar() {
        // Check if sidebar window already exists
        chrome.storage.local.get(['sidebarWindowId'], (result) => {
            if (result.sidebarWindowId) {
                // Try to focus existing sidebar window
                chrome.windows.get(result.sidebarWindowId, (window) => {
                    if (chrome.runtime.lastError || !window) {
                        // Window doesn't exist, create new one
                        this.createSidebarWindow();
                    } else {
                        // Focus existing window
                        chrome.windows.update(result.sidebarWindowId, { focused: true });
                    }
                });
            } else {
                // Create new sidebar window
                this.createSidebarWindow();
            }
        });
    }

    createSidebarWindow() {
        const width = Math.max(350, Math.min(500, window.screen.width * 0.3));
        const height = window.screen.height - 100;
        const left = window.screen.width - width - 50;
        const top = 50;

        chrome.windows.create({
            url: chrome.runtime.getURL('popup.html?sidebar=true'),
            type: 'popup',
            width: width,
            height: height,
            left: left,
            top: top
        }, (window) => {
            // Store sidebar window ID for persistence
            chrome.storage.local.set({ sidebarWindowId: window.id });
        });
    }

    checkSidebarMode() {
        // Check if we're in sidebar mode based on URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const isSidebar = urlParams.get('sidebar') === 'true';

        if (isSidebar) {
            // Add sidebar-specific styling
            document.body.classList.add('sidebar-mode');

            // Set up auto-refresh for sidebar mode
            this.setupSidebarRefresh();
        } else if (this.settings.sidebarMode && !window.location.search.includes('sidebar')) {
            // If sidebar mode is enabled as default and we're not already in sidebar, open it
            setTimeout(() => {
                this.openSidebar();
                window.close(); // Close the popup since we're opening sidebar
            }, 100);
        }
    }

    setupSidebarRefresh() {
        // Listen for messages from background script about tab changes
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'tabChanged' || message.action === 'activeTabChanged') {
                // Refresh current tab info and update UI
                this.loadCurrentTab().then(() => {
                    this.updateUI();
                });
            }
        });

        // Listen for tab changes directly (backup method)
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async () => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Periodic refresh as final backup
        setInterval(async () => {
            await this.loadCurrentTab();
            this.updateUI();
        }, 10000); // Reduced frequency since we have event-based updates
    }

    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.globalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in global mode
            baseUrl: this.getScopeBaseUrl(),
            notes: '' // Add notes field for todo-note relationships
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddTodo();
    }
    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!content) return;

        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.globalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in global mode
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null // For linking notes to todos
        };

        this.notes.unshift(note); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddNote();
    }
    
    getScopeUrl() {
        // Always save full URL, but scope determines what we show
        return this.currentTab?.url || '';
    }

    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }

    getFilteredTodos() {
        const allTodos = this.todos;

        // If in global mode, only show global todos
        if (this.globalMode) {
            return allTodos.filter(todo => todo.url === 'global');
        }

        switch (this.currentScope) {
            case 'current-tab':
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allTodos.filter(todo => {
                    try {
                        if (todo.url === 'global') return false; // Exclude global todos from website view
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allTodos.filter(todo => todo.url !== 'global'); // Exclude global todos from all view unless in global mode
            default:
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
        }
    }

    getFilteredNotes() {
        const allNotes = this.notes.filter(note => !note.archived);

        // If in global mode, only show global notes
        if (this.globalMode) {
            return allNotes.filter(note => note.url === 'global');
        }

        switch (this.currentScope) {
            case 'current-tab':
                return allNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allNotes.filter(note => {
                    try {
                        if (note.url === 'global') return false; // Exclude global notes from website view
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allNotes.filter(note => note.url !== 'global'); // Exclude global notes from all view unless in global mode
            default:
                return allNotes.filter(note => note.url === this.currentTab?.url);
        }
    }

    updateUI() {
        this.updateTodosList();
        this.updateNotesList();
        this.updateCompletedTodos();
        this.updateCounts();

        // Update placeholder text based on current scope and global mode
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        if (this.globalMode) {
            if (todoInput) todoInput.placeholder = 'Add a global todo (not linked to any URL)...';
            if (noteContent) noteContent.placeholder = 'Add a global note (not linked to any URL)...';
        } else {
            switch (this.currentScope) {
                case 'current-tab':
                    if (todoInput) todoInput.placeholder = 'Add a new todo for this page...';
                    if (noteContent) noteContent.placeholder = 'Add a new note for this page...';
                    break;
                case 'website':
                    if (todoInput) todoInput.placeholder = 'Add a new todo for this website...';
                    if (noteContent) noteContent.placeholder = 'Add a new note for this website...';
                    break;
                case 'all':
                    if (todoInput) todoInput.placeholder = 'Add a new todo...';
                    if (noteContent) noteContent.placeholder = 'Add a new note...';
                    break;
            }
        }

        this.updateGlobalModeDisplay();
    }

    updateGlobalModeDisplay() {
        const globalModeToggle = document.getElementById('global-mode-toggle');
        const scopeSelector = document.getElementById('scope-selector');

        if (globalModeToggle) {
            globalModeToggle.checked = this.globalMode;
        }

        // Disable scope selector when in global mode
        if (scopeSelector) {
            scopeSelector.disabled = this.globalMode;
            scopeSelector.style.opacity = this.globalMode ? '0.5' : '1';
        }
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'All Items': items };
        }

        const groups = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'website':
                    try {
                        const url = new URL(item.url);
                        // Extract base URL (hostname + path without query/hash)
                        groupKey = url.hostname + (url.pathname !== '/' ? url.pathname : '');
                    } catch (error) {
                        groupKey = 'Global';
                    }
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        const date = new Date(item.dueDate);
                        groupKey = date.toDateString();
                    } else {
                        groupKey = 'No Due Date';
                    }
                    break;
                case 'created-date':
                    const createdDate = new Date(item.createdAt);
                    groupKey = createdDate.toDateString();
                    break;
                default:
                    groupKey = 'All Items';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
        });

        // Sort groups for due-date: items with due dates first, then no due date
        if (groupBy === 'due-date') {
            const sortedGroups = {};
            const withDueDates = [];
            const withoutDueDates = [];

            Object.keys(groups).forEach(key => {
                if (key === 'No Due Date') {
                    withoutDueDates.push(key);
                } else {
                    withDueDates.push(key);
                }
            });

            // Sort dates chronologically
            withDueDates.sort((a, b) => new Date(a) - new Date(b));

            // Add with due dates first, then without
            [...withDueDates, ...withoutDueDates].forEach(key => {
                sortedGroups[key] = groups[key];
            });

            return sortedGroups;
        }

        return groups;
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const filteredTodos = this.getFilteredTodos().filter(todo => !todo.completed);

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '';
            return;
        }

        todosList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                todosList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredTodos.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });
        }
    }

    updateCompletedTodos() {
        const completedList = document.getElementById('completed-todos-list');
        const completedTodos = this.getFilteredTodos().filter(todo => todo.completed);
        const completedSection = document.getElementById('completed-todos');

        if (completedTodos.length === 0 || !this.settings.showCompleted) {
            completedSection.style.display = 'none';
            return;
        }

        completedSection.style.display = 'block';
        completedList.innerHTML = '';
        completedTodos.forEach(todo => {
            const todoEl = this.createTodoElement(todo);
            completedList.appendChild(todoEl);
        });
    }

    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '';
            return;
        }

        notesList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                notesList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            filteredNotes.forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });
        }
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''}`;

        const dueDateDisplay = this.formatDueDate(todo.dueDate);
        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';

        todoEl.innerHTML = `
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
            <div class="item-content">
                ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">🔗</a>` : ''}
                <div class="item-text" data-todo-id="${todo.id}" style="cursor: pointer;">${todo.text}</div>
                <div class="item-meta">
                    ${dueDateDisplay ? `<span class="item-due-date ${this.getDueDateClass(todo.dueDate)}">${dueDateDisplay}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-btn" data-todo-id="${todo.id}" title="Edit">✏️</button>
                <button class="date-btn" data-todo-id="${todo.id}" title="Set Due Date">📅</button>
                <button class="delete-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const checkbox = todoEl.querySelector('.item-checkbox');
        checkbox.addEventListener('change', () => this.toggleTodo(todo.id));

        const textEl = todoEl.querySelector('.item-text');
        textEl.addEventListener('click', () => this.editTodo(todo.id));

        const editBtn = todoEl.querySelector('.edit-btn');
        editBtn.addEventListener('click', () => this.editTodo(todo.id));

        const dateBtn = todoEl.querySelector('.date-btn');
        dateBtn.addEventListener('click', () => this.setDueDate(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';

        const createdDate = new Date(note.createdAt).toLocaleDateString();

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">🔗</a> ` : ''}
                    ${note.title ? `<strong>${note.title}</strong> <span class="created-date">${createdDate}</span><br>` : ''}
                    ${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                <button class="archive-note-btn" data-note-id="${note.id}" title="Archive">📁</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const editBtn = noteEl.querySelector('.edit-note-btn');
        editBtn.addEventListener('click', () => this.editNote(note.id));

        const archiveBtn = noteEl.querySelector('.archive-note-btn');
        archiveBtn.addEventListener('click', () => this.archiveNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }



    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else {
            return due.toLocaleDateString();
        }
    }

    getDueDateClass(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'tomorrow';
        } else {
            return '';
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveData();
            this.updateUI(); // Update both active and completed lists
        }
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const newText = prompt('Edit todo:', todo.text);
            if (newText !== null && newText.trim()) {
                todo.text = newText.trim();
                this.saveData();
                this.updateUI();
            }
        }
    }

    setDueDate(todoId) {
        this.currentEditingTodoId = todoId;
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const currentDate = todo.dueDate || '';
            document.getElementById('date-picker-input').value = currentDate;
            document.getElementById('date-picker-modal').style.display = 'flex';
        }
    }

    deleteTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Todo deleted', () => {
                this.todos.push(todoBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            this.currentEditingNoteId = noteId;

            const titleEl = document.getElementById('edit-note-title');
            const contentEl = document.getElementById('edit-note-content');
            const modalEl = document.getElementById('edit-note-modal');

            if (titleEl) titleEl.value = note.title;
            if (contentEl) contentEl.value = note.content;
            if (modalEl) modalEl.style.display = 'flex';
        }
    }

    closeEditNoteModal() {
        const modalEl = document.getElementById('edit-note-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
        this.currentEditingNoteId = null;
    }

    saveEditedNote() {
        if (!this.currentEditingNoteId) return;

        const note = this.notes.find(n => n.id === this.currentEditingNoteId);
        if (!note) return;

        const titleEl = document.getElementById('edit-note-title');
        const contentEl = document.getElementById('edit-note-content');

        const newTitle = titleEl ? titleEl.value.trim() : '';
        const newContent = contentEl ? contentEl.value.trim() : '';

        if (newContent) {
            note.title = newTitle || newContent.split('\n')[0].substring(0, 50);
            note.content = newContent;
            this.saveData();
            this.updateUI();
        }

        this.closeEditNoteModal();
    }

    linkToTodo(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const availableTodos = this.getFilteredTodos();
            if (availableTodos.length === 0) {
                alert('No todos available to link to. Create a todo first.');
                return;
            }

            const todoOptions = availableTodos.map(todo => `${todo.id}: ${todo.text}`).join('\n');
            const selectedTodoId = prompt(`Link to which todo?\n\n${todoOptions}\n\nEnter todo ID:`);

            if (selectedTodoId && availableTodos.find(t => t.id === selectedTodoId)) {
                note.linkedTodoId = selectedTodoId;
                this.saveData();
                this.updateUI();
                alert('Note linked to todo successfully!');
            }
        }
    }

    archiveNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            note.archived = true;
            note.archivedAt = new Date().toISOString();
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note archived', () => {
                const restoredNote = this.notes.find(n => n.id === noteId);
                if (restoredNote) {
                    restoredNote.archived = false;
                    delete restoredNote.archivedAt;
                    this.saveData();
                    this.updateUI();
                }
            });
        }
    }

    deleteNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note deleted', () => {
                this.notes.push(noteBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    saveSettings() {
        chrome.storage.local.set({
            settings: this.settings
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // New UI methods
    showAddTodo() {
        document.getElementById('add-todo-section').style.display = 'block';
        document.getElementById('todo-input').focus();
    }

    hideAddTodo() {
        document.getElementById('add-todo-section').style.display = 'none';
        document.getElementById('todo-input').value = '';
        document.getElementById('todo-due-date').style.display = 'none';
        document.getElementById('todo-due-date').value = '';
    }

    showAddNote() {
        document.getElementById('add-note-section').style.display = 'block';
        document.getElementById('note-title').focus();
    }

    hideAddNote() {
        document.getElementById('add-note-section').style.display = 'none';
        document.getElementById('note-title').value = '';
        document.getElementById('note-content').value = '';
    }

    showDatePicker() {
        const todoDueDateEl = document.getElementById('todo-due-date');
        const datePickerInputEl = document.getElementById('date-picker-input');
        const modalEl = document.getElementById('date-picker-modal');

        const currentDate = todoDueDateEl ? todoDueDateEl.value : '';
        if (datePickerInputEl) {
            datePickerInputEl.value = currentDate;
        }
        if (modalEl) {
            modalEl.style.display = 'flex';
        }
    }

    setSelectedDate() {
        const datePickerInput = document.getElementById('date-picker-input');
        const selectedDate = datePickerInput ? datePickerInput.value : '';

        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = selectedDate || null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');
            if (todoDueDateEl) {
                todoDueDateEl.value = selectedDate;
                todoDueDateEl.style.display = selectedDate ? 'block' : 'none';
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    clearSelectedDate() {
        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');
            if (todoDueDateEl) {
                todoDueDateEl.value = '';
                todoDueDateEl.style.display = 'none';
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    toggleSection(sectionName) {
        const content = document.getElementById(`${sectionName}-content`);
        const btn = document.querySelector(`[data-section="${sectionName}"] .collapse-btn`);

        if (!content || !btn) return;

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            btn.classList.remove('collapsed');
            btn.textContent = '▼';
        } else {
            content.classList.add('collapsed');
            btn.classList.add('collapsed');
            btn.textContent = '▶';
        }
    }



    showUndoToast(message, undoCallback) {
        this.undoCallback = undoCallback;
        const messageEl = document.getElementById('undo-message');
        const toastEl = document.getElementById('undo-toast');
        const progressBarEl = document.getElementById('toast-progress-bar');

        if (messageEl) messageEl.textContent = message;
        if (toastEl) toastEl.style.display = 'block';

        // Reset progress bar
        if (progressBarEl) {
            progressBarEl.style.width = '100%';
        }

        // Clear any existing timeout
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start progress bar animation
        let timeLeft = 20000; // 20 seconds
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            timeLeft -= updateInterval;
            const percentage = (timeLeft / 20000) * 100;

            if (progressBarEl) {
                progressBarEl.style.width = `${Math.max(0, percentage)}%`;
            }

            if (timeLeft <= 0) {
                this.hideUndoToast();
            }
        }, updateInterval);

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    hideUndoToast() {
        const toastEl = document.getElementById('undo-toast');
        if (toastEl) {
            toastEl.style.display = 'none';
        }

        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
            this.undoTimeout = null;
        }

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        this.undoCallback = null;
    }

    undoDelete() {
        if (this.undoCallback) {
            this.undoCallback();
            this.hideUndoToast();
        }
    }

    updateCounts() {
        const filteredTodos = this.getFilteredTodos();
        const completedTodos = filteredTodos.filter(todo => todo.completed);
        const activeTodos = filteredTodos.filter(todo => !todo.completed);
        const filteredNotes = this.getFilteredNotes();

        document.getElementById('todos-count').textContent = `(${activeTodos.length})`;
        document.getElementById('completed-count').textContent = `(${completedTodos.length})`;
        document.getElementById('notes-count').textContent = `(${filteredNotes.length})`;
    }

    showCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'none';
        if (completedPage) completedPage.style.display = 'block';

        this.updateCompletedArchivedPage();
    }

    hideCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'block';
        if (completedPage) completedPage.style.display = 'none';
    }

    updateCompletedArchivedPage() {
        // Update completed todos
        const completedTodos = this.todos.filter(todo => todo.completed);
        const completedPageList = document.getElementById('completed-page-list');
        const completedPageCount = document.getElementById('completed-page-count');

        if (completedPageList) {
            completedPageList.innerHTML = '';
            completedTodos.forEach(todo => {
                const todoEl = this.createCompletedTodoElement(todo);
                completedPageList.appendChild(todoEl);
            });
        }

        if (completedPageCount) {
            completedPageCount.textContent = `(${completedTodos.length})`;
        }

        // Update archived notes
        const archivedNotes = this.notes.filter(note => note.archived);
        const archivedPageList = document.getElementById('archived-page-list');
        const archivedPageCount = document.getElementById('archived-page-count');

        if (archivedPageList) {
            archivedPageList.innerHTML = '';
            archivedNotes.forEach(note => {
                const noteEl = this.createArchivedNoteElement(note);
                archivedPageList.appendChild(noteEl);
            });
        }

        if (archivedPageCount) {
            archivedPageCount.textContent = `(${archivedNotes.length})`;
        }
    }

    createCompletedTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = 'item completed';

        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';
        const completedDate = todo.completedAt ? new Date(todo.completedAt).toLocaleDateString() : '';

        todoEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">🔗</a> ` : ''}
                    <span class="todo-text">${todo.text}</span>
                    ${completedDate ? `<span class="completed-date"> - Completed ${completedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-todo-btn" data-todo-id="${todo.id}" title="Restore">↩️</button>
                <button class="delete-todo-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = todoEl.querySelector('.restore-todo-btn');
        restoreBtn.addEventListener('click', () => this.restoreTodo(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-todo-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createArchivedNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item archived';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';
        const archivedDate = note.archivedAt ? new Date(note.archivedAt).toLocaleDateString() : '';

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">🔗</a> ` : ''}
                    ${note.title ? `<strong>${note.title}</strong><br>` : ''}
                    ${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                    ${archivedDate ? `<span class="archived-date"> - Archived ${archivedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-note-btn" data-note-id="${note.id}" title="Restore">↩️</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = noteEl.querySelector('.restore-note-btn');
        restoreBtn.addEventListener('click', () => this.restoreNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }

    restoreTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = false;
            delete todo.completedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    restoreNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            note.archived = false;
            delete note.archivedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();
        }
    }

    openFullscreen() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('fullscreen.html')
        });
    }

    initFullscreenMode() {
        // Check if we're in fullscreen mode
        if (!document.body.classList.contains('fullscreen')) {
            return;
        }

        // Set up fullscreen-specific event listeners
        const addGlobalTodoBtnEl = document.getElementById('add-global-todo-btn');
        if (addGlobalTodoBtnEl) {
            addGlobalTodoBtnEl.addEventListener('click', () => {
                this.addGlobalTodo();
            });
        }

        const addGlobalNoteBtnEl = document.getElementById('add-global-note-btn');
        if (addGlobalNoteBtnEl) {
            addGlobalNoteBtnEl.addEventListener('click', () => {
                this.addGlobalNote();
            });
        }

        // Load and display global items
        this.loadData().then(() => {
            this.updateGlobalTodosList();
            this.updateGlobalNotesList();
        });
    }

    addGlobalTodo() {
        const textEl = document.getElementById('global-todo-text');
        const dateEl = document.getElementById('global-todo-date');

        const text = textEl ? textEl.value.trim() : '';
        const dueDate = dateEl ? dateEl.value : '';

        if (text) {
            const todo = {
                id: Date.now().toString(),
                text: text,
                completed: false,
                url: 'global',
                createdAt: new Date().toISOString(),
                dueDate: dueDate || null
            };

            this.todos.unshift(todo);
            this.saveData();
            this.updateGlobalTodosList();

            // Clear form
            if (textEl) textEl.value = '';
            if (dateEl) dateEl.value = '';
        }
    }

    addGlobalNote() {
        const titleEl = document.getElementById('global-note-title');
        const contentEl = document.getElementById('global-note-content');

        const title = titleEl ? titleEl.value.trim() : '';
        const content = contentEl ? contentEl.value.trim() : '';

        if (content) {
            const note = {
                id: Date.now().toString(),
                title: title || content.split('\n')[0].substring(0, 50),
                content: content,
                url: 'global',
                createdAt: new Date().toISOString()
            };

            this.notes.unshift(note);
            this.saveData();
            this.updateGlobalNotesList();

            // Clear form
            if (titleEl) titleEl.value = '';
            if (contentEl) contentEl.value = '';
        }
    }

    updateGlobalTodosList() {
        const listEl = document.getElementById('global-todos-list');
        if (!listEl) return;

        const globalTodos = this.todos.filter(todo => todo.url === 'global' && !todo.completed);
        listEl.innerHTML = '';

        globalTodos.forEach(todo => {
            const todoEl = this.createTodoElement(todo);
            listEl.appendChild(todoEl);
        });
    }

    updateGlobalNotesList() {
        const listEl = document.getElementById('global-notes-list');
        if (!listEl) return;

        const globalNotes = this.notes.filter(note => note.url === 'global' && !note.archived);
        listEl.innerHTML = '';

        globalNotes.forEach(note => {
            const noteEl = this.createNoteElement(note);
            listEl.appendChild(noteEl);
        });
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
    // Make app globally available for inline event handlers
    window.app = app;
});
