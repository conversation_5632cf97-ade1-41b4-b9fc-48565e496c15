// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'all'; // Default to all view
        this.currentGrouping = 'none';
        this.currentEditingTodoId = null;
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        this.settings = {
            layout: 'current-tab', // 'current-tab' or 'todos-top'
            sidebarMode: false,
            showCompleted: true
        };

        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();
        this.checkSidebarMode();
        this.updateUI();

        // Ensure counts are updated after everything is loaded
        setTimeout(() => {
            this.updateCounts();
        }, 100);
    }
    
    async loadCurrentTab() {
        try {
            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
            
            if (this.currentTab) {
                document.getElementById('current-url').textContent = 
                    this.formatUrl(this.currentTab.url);
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache', 'settings'], resolve);
            });

            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
            this.settings = { ...this.settings, ...(result.settings || {}) };
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Scope selection
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchScope(e.target.dataset.scope);
            });
        });

        // Grouping selection
        document.getElementById('group-by').addEventListener('change', (e) => {
            this.currentGrouping = e.target.value;
            this.updateUI();
        });

        // New item buttons
        document.getElementById('new-todo-btn').addEventListener('click', () => {
            this.showAddTodo();
        });

        document.getElementById('new-note-btn').addEventListener('click', () => {
            this.showAddNote();
        });

        // Add todo
        document.getElementById('add-todo-btn').addEventListener('click', () => {
            this.addTodo();
        });

        document.getElementById('cancel-todo-btn').addEventListener('click', () => {
            this.hideAddTodo();
        });

        document.getElementById('todo-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTodo();
            }
        });

        // Calendar button
        document.getElementById('calendar-btn').addEventListener('click', () => {
            this.showDatePicker();
        });

        // Add note
        document.getElementById('add-note-btn').addEventListener('click', () => {
            this.addNote();
        });

        document.getElementById('cancel-note-btn').addEventListener('click', () => {
            this.hideAddNote();
        });

        // Settings
        document.getElementById('settings-btn').addEventListener('click', () => {
            this.openSettings();
        });

        document.getElementById('close-settings').addEventListener('click', () => {
            this.closeSettings();
        });

        // Toggle completed todos
        document.getElementById('toggle-completed').addEventListener('click', (e) => {
            this.settings.showCompleted = !this.settings.showCompleted;
            e.target.textContent = this.settings.showCompleted ? 'Hide Completed' : 'Show Completed';
            this.updateUI();
            this.saveSettings();
        });

        // Sidebar mode
        document.getElementById('sidebar-btn').addEventListener('click', () => {
            this.openSidebar();
        });

        // Settings form
        document.getElementById('layout-setting').addEventListener('change', (e) => {
            this.settings.layout = e.target.value;
            this.saveSettings();
            this.updateUI();
        });

        document.getElementById('sidebar-mode').addEventListener('change', (e) => {
            this.settings.sidebarMode = e.target.checked;
            this.saveSettings();
        });

        // Date picker modal
        document.getElementById('close-date-picker').addEventListener('click', () => {
            document.getElementById('date-picker-modal').style.display = 'none';
        });

        document.getElementById('set-date-btn').addEventListener('click', () => {
            this.setSelectedDate();
        });

        document.getElementById('clear-date-btn').addEventListener('click', () => {
            this.clearSelectedDate();
        });

        // Collapsible sections
        document.querySelectorAll('.section-header.collapsible').forEach(header => {
            header.addEventListener('click', (e) => {
                if (e.target.classList.contains('collapse-btn')) return;
                this.toggleSection(header.dataset.section);
            });
        });

        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sectionHeader = e.target.closest('.section-header') || e.target.closest('.completed-header');
                if (sectionHeader && sectionHeader.dataset.section) {
                    this.toggleSection(sectionHeader.dataset.section);
                }
            });
        });

        // Undo toast
        document.getElementById('undo-btn').addEventListener('click', () => {
            this.undoDelete();
        });

        document.getElementById('close-toast').addEventListener('click', () => {
            this.hideUndoToast();
        });
    }
    
    switchScope(scopeName) {
        // Update scope buttons
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-scope="${scopeName}"]`).classList.add('active');

        this.currentScope = scopeName;

        // Show/hide grouping selector based on scope
        const groupingSelector = document.getElementById('grouping-selector');
        if (scopeName === 'all') {
            groupingSelector.style.display = 'flex';
        } else {
            groupingSelector.style.display = 'none';
        }

        this.updateUI();
    }

    openSettings() {
        document.getElementById('settings-modal').style.display = 'flex';
        document.getElementById('layout-setting').value = this.settings.layout;
        document.getElementById('sidebar-mode').checked = this.settings.sidebarMode;
    }

    closeSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    openSidebar() {
        // Create a new window for sidebar mode
        const width = Math.max(350, Math.min(500, window.screen.width * 0.3));
        const height = window.screen.height - 100;
        const left = window.screen.width - width - 50;
        const top = 50;

        chrome.windows.create({
            url: chrome.runtime.getURL('popup.html?sidebar=true'),
            type: 'popup',
            width: width,
            height: height,
            left: left,
            top: top
        }, (window) => {
            // Store sidebar window ID for persistence
            chrome.storage.local.set({ sidebarWindowId: window.id });
        });
    }

    checkSidebarMode() {
        // Check if we're in sidebar mode based on URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const isSidebar = urlParams.get('sidebar') === 'true';

        if (isSidebar) {
            // Add sidebar-specific styling
            document.body.classList.add('sidebar-mode');

            // Set up auto-refresh for sidebar mode
            this.setupSidebarRefresh();
        }
    }

    setupSidebarRefresh() {
        // Refresh data every 5 seconds when in sidebar mode
        setInterval(async () => {
            await this.getCurrentTab();
            this.updateUI();
        }, 5000);

        // Listen for tab changes
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async () => {
                await this.getCurrentTab();
                this.updateUI();
            });
        }
    }

    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.currentTab?.url || 'global', // Always save full URL
            baseUrl: this.getScopeBaseUrl(),
            notes: '' // Add notes field for todo-note relationships
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddTodo();
    }
    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!content) return;

        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.currentTab?.url || 'global', // Always save full URL
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null // For linking notes to todos
        };

        this.notes.unshift(note); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddNote();
    }
    
    getScopeUrl() {
        // Always save full URL, but scope determines what we show
        return this.currentTab?.url || '';
    }

    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }

    getFilteredTodos() {
        const allTodos = this.todos;

        switch (this.currentScope) {
            case 'current-tab':
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allTodos.filter(todo => {
                    try {
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allTodos;
            default:
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
        }
    }

    getFilteredNotes() {
        const allNotes = this.notes;

        switch (this.currentScope) {
            case 'current-tab':
                return allNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allNotes.filter(note => {
                    try {
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allNotes;
            default:
                return allNotes.filter(note => note.url === this.currentTab?.url);
        }
    }

    updateUI() {
        this.updateTodosList();
        this.updateNotesList();
        this.updateCompletedTodos();
        this.updateCounts();

        // Update placeholder text based on current scope
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        switch (this.currentScope) {
            case 'current-tab':
                todoInput.placeholder = 'Add a new todo for this page...';
                noteContent.placeholder = 'Add a new note for this page...';
                break;
            case 'website':
                todoInput.placeholder = 'Add a new todo for this website...';
                noteContent.placeholder = 'Add a new note for this website...';
                break;
            case 'all':
                todoInput.placeholder = 'Add a new todo...';
                noteContent.placeholder = 'Add a new note...';
                break;
        }
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'All Items': items };
        }

        const groups = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'website':
                    try {
                        const url = new URL(item.url);
                        groupKey = url.hostname;
                    } catch (error) {
                        groupKey = 'Global';
                    }
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        const date = new Date(item.dueDate);
                        groupKey = date.toDateString();
                    } else {
                        groupKey = 'No Due Date';
                    }
                    break;
                case 'created-date':
                    const createdDate = new Date(item.createdAt);
                    groupKey = createdDate.toDateString();
                    break;
                default:
                    groupKey = 'All Items';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
        });

        return groups;
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const filteredTodos = this.getFilteredTodos().filter(todo => !todo.completed);

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '';
            return;
        }

        todosList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;
                todosList.appendChild(groupHeader);

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    todosList.appendChild(todoEl);
                });
            });
        } else {
            filteredTodos.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });
        }
    }

    updateCompletedTodos() {
        const completedList = document.getElementById('completed-todos-list');
        const completedTodos = this.getFilteredTodos().filter(todo => todo.completed);
        const completedSection = document.getElementById('completed-todos');

        if (completedTodos.length === 0 || !this.settings.showCompleted) {
            completedSection.style.display = 'none';
            return;
        }

        completedSection.style.display = 'block';
        completedList.innerHTML = '';
        completedTodos.forEach(todo => {
            const todoEl = this.createTodoElement(todo);
            completedList.appendChild(todoEl);
        });
    }

    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '';
            return;
        }

        notesList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;
                notesList.appendChild(groupHeader);

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    notesList.appendChild(noteEl);
                });
            });
        } else {
            filteredNotes.forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });
        }
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''}`;

        const dueDateDisplay = this.formatDueDate(todo.dueDate);
        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';

        todoEl.innerHTML = `
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
            <div class="item-content">
                ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">🔗</a>` : ''}
                <div class="item-text" data-todo-id="${todo.id}" style="cursor: pointer;">${todo.text}</div>
                <div class="item-meta">
                    ${dueDateDisplay ? `<span class="item-due-date ${this.getDueDateClass(todo.dueDate)}">${dueDateDisplay}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-btn" data-todo-id="${todo.id}" title="Edit">✏️</button>
                <button class="date-btn" data-todo-id="${todo.id}" title="Set Due Date">📅</button>
                <button class="delete-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const checkbox = todoEl.querySelector('.item-checkbox');
        checkbox.addEventListener('change', () => this.toggleTodo(todo.id));

        const textEl = todoEl.querySelector('.item-text');
        textEl.addEventListener('click', () => this.editTodo(todo.id));

        const editBtn = todoEl.querySelector('.edit-btn');
        editBtn.addEventListener('click', () => this.editTodo(todo.id));

        const dateBtn = todoEl.querySelector('.date-btn');
        dateBtn.addEventListener('click', () => this.setDueDate(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';

        const createdDate = new Date(note.createdAt).toLocaleDateString();

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">🔗</a> ` : ''}
                    ${note.title ? `<strong>${note.title}</strong> <span class="created-date">${createdDate}</span><br>` : ''}
                    ${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const editBtn = noteEl.querySelector('.edit-note-btn');
        editBtn.addEventListener('click', () => this.editNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'all': items };
        }

        const grouped = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'url':
                    groupKey = this.formatUrl(item.url);
                    break;
                case 'due-date':
                    if (item.dueDate) {
                        groupKey = new Date(item.dueDate).toDateString();
                    } else {
                        groupKey = 'No due date';
                    }
                    break;
                case 'created-date':
                    groupKey = new Date(item.createdAt).toDateString();
                    break;
                default:
                    groupKey = 'Other';
            }

            if (!grouped[groupKey]) {
                grouped[groupKey] = [];
            }
            grouped[groupKey].push(item);
        });

        return grouped;
    }

    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else {
            return due.toLocaleDateString();
        }
    }

    getDueDateClass(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'tomorrow';
        } else {
            return '';
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveData();
            this.updateUI(); // Update both active and completed lists
        }
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const newText = prompt('Edit todo:', todo.text);
            if (newText !== null && newText.trim()) {
                todo.text = newText.trim();
                this.saveData();
                this.updateUI();
            }
        }
    }

    setDueDate(todoId) {
        this.currentEditingTodoId = todoId;
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const currentDate = todo.dueDate || '';
            document.getElementById('date-picker-input').value = currentDate;
            document.getElementById('date-picker-modal').style.display = 'flex';
        }
    }

    deleteTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Todo deleted', () => {
                this.todos.push(todoBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const newTitle = prompt('Edit note title:', note.title);
            if (newTitle !== null) {
                const newContent = prompt('Edit note content:', note.content);
                if (newContent !== null && newContent.trim()) {
                    note.title = newTitle.trim() || newContent.split('\n')[0].substring(0, 50);
                    note.content = newContent.trim();
                    this.saveData();
                    this.updateUI();
                }
            }
        }
    }

    linkToTodo(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const availableTodos = this.getFilteredTodos();
            if (availableTodos.length === 0) {
                alert('No todos available to link to. Create a todo first.');
                return;
            }

            const todoOptions = availableTodos.map(todo => `${todo.id}: ${todo.text}`).join('\n');
            const selectedTodoId = prompt(`Link to which todo?\n\n${todoOptions}\n\nEnter todo ID:`);

            if (selectedTodoId && availableTodos.find(t => t.id === selectedTodoId)) {
                note.linkedTodoId = selectedTodoId;
                this.saveData();
                this.updateUI();
                alert('Note linked to todo successfully!');
            }
        }
    }

    deleteNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note deleted', () => {
                this.notes.push(noteBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    saveSettings() {
        chrome.storage.local.set({
            settings: this.settings
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // New UI methods
    showAddTodo() {
        document.getElementById('add-todo-section').style.display = 'block';
        document.getElementById('todo-input').focus();
    }

    hideAddTodo() {
        document.getElementById('add-todo-section').style.display = 'none';
        document.getElementById('todo-input').value = '';
        document.getElementById('todo-due-date').style.display = 'none';
        document.getElementById('todo-due-date').value = '';
    }

    showAddNote() {
        document.getElementById('add-note-section').style.display = 'block';
        document.getElementById('note-title').focus();
    }

    hideAddNote() {
        document.getElementById('add-note-section').style.display = 'none';
        document.getElementById('note-title').value = '';
        document.getElementById('note-content').value = '';
    }

    showDatePicker() {
        const currentDate = document.getElementById('todo-due-date').value;
        document.getElementById('date-picker-input').value = currentDate;
        document.getElementById('date-picker-modal').style.display = 'flex';
    }

    setSelectedDate() {
        const selectedDate = document.getElementById('date-picker-input').value;

        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = selectedDate || null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            document.getElementById('todo-due-date').value = selectedDate;
            document.getElementById('todo-due-date').style.display = selectedDate ? 'block' : 'none';
        }

        document.getElementById('date-picker-modal').style.display = 'none';
    }

    clearSelectedDate() {
        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            document.getElementById('todo-due-date').value = '';
            document.getElementById('todo-due-date').style.display = 'none';
        }

        document.getElementById('date-picker-modal').style.display = 'none';
    }

    toggleSection(sectionName) {
        const content = document.getElementById(`${sectionName}-content`);
        const btn = document.querySelector(`[data-section="${sectionName}"] .collapse-btn`);

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            btn.classList.remove('collapsed');
            btn.textContent = '▼';
        } else {
            content.classList.add('collapsed');
            btn.classList.add('collapsed');
            btn.textContent = '▶';
        }
    }



    showUndoToast(message, undoCallback) {
        this.undoCallback = undoCallback;
        document.getElementById('undo-message').textContent = message;
        document.getElementById('undo-toast').style.display = 'block';

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    hideUndoToast() {
        document.getElementById('undo-toast').style.display = 'none';
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        this.undoCallback = null;
    }

    undoDelete() {
        if (this.undoCallback) {
            this.undoCallback();
            this.hideUndoToast();
        }
    }

    updateCounts() {
        const filteredTodos = this.getFilteredTodos();
        const completedTodos = filteredTodos.filter(todo => todo.completed);
        const activeTodos = filteredTodos.filter(todo => !todo.completed);
        const filteredNotes = this.getFilteredNotes();

        document.getElementById('todos-count').textContent = `(${activeTodos.length})`;
        document.getElementById('completed-count').textContent = `(${completedTodos.length})`;
        document.getElementById('notes-count').textContent = `(${filteredNotes.length})`;
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
    // Make app globally available for inline event handlers
    window.app = app;
});
