// Background service worker for Todo Notes Extension

// Initialize extension on install
chrome.runtime.onInstalled.addListener(() => {
    console.log('Todo Notes Extension installed');

    // Initialize storage structure if not exists
    chrome.storage.local.get(['todos', 'notes', 'urlCache'], (result) => {
        if (!result.todos) {
            chrome.storage.local.set({ todos: [] });
        }
        if (!result.notes) {
            chrome.storage.local.set({ notes: [] });
        }
        if (!result.urlCache) {
            chrome.storage.local.set({ urlCache: {} });
        }
    });
});

// Track side panel state per window
const sidePanelState = new Map();

// Handle extension icon click - check user preference for mode
chrome.action.onClicked.addListener(async (tab) => {
    // Get user preference for default mode
    const result = await chrome.storage.local.get(['settings']);
    const settings = result.settings || {};

    if (settings.floatingWindowMode) {
        // Open as floating window
        openFloatingWindow();
    } else if (settings.sidebarMode !== false) {
        // Open as side panel (default)
        openSidePanel(tab.windowId);
    } else {
        // Open as popup
        openPopupWindow();
    }
});

async function openSidePanel(windowId) {
    const isOpen = sidePanelState.get(windowId) || false;

    if (isOpen) {
        // Close the side panel
        try {
            await chrome.sidePanel.setOptions({
                enabled: false
            });
            sidePanelState.set(windowId, false);
        } catch (error) {
            console.log('Could not close side panel:', error);
        }
    } else {
        // Open the side panel
        try {
            await chrome.sidePanel.setOptions({
                enabled: true
            });
            await chrome.sidePanel.open({ windowId: windowId });
            sidePanelState.set(windowId, true);
        } catch (error) {
            console.log('Could not open side panel:', error);
            // Fallback to popup if side panel fails
            openPopupWindow();
        }
    }
}

function openFloatingWindow() {
    chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 400,
        height: 600,
        focused: true
    });
}

function openPopupWindow() {
    chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 380,
        height: 580,
        focused: true
    });
}

// Track when windows are closed to clean up state
chrome.windows.onRemoved.addListener((windowId) => {
    sidePanelState.delete(windowId);
});

// Track tab changes to cache URLs
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        cacheTabUrl(tabId, tab.url);
    }
});

// Track tab activation (switching between tabs)
chrome.tabs.onActivated.addListener((activeInfo) => {
    chrome.tabs.get(activeInfo.tabId, (tab) => {
        if (tab && tab.url) {
            // Tab changed - side panel will automatically update
            console.log(`Active tab changed to: ${tab.url}`);
        }
    });
});

// Track tab removal to maintain cache
chrome.tabs.onRemoved.addListener((tabId) => {
    // Keep URL in cache even after tab is closed
    // This allows todos/notes to persist for closed tabs
    console.log(`Tab ${tabId} closed, URL cached for future reference`);
});

// Cache tab URLs for persistence
function cacheTabUrl(tabId, url) {
    chrome.storage.local.get(['urlCache'], (result) => {
        const urlCache = result.urlCache || {};
        urlCache[tabId] = {
            url: url,
            timestamp: Date.now(),
            baseUrl: extractBaseUrl(url)
        };
        
        chrome.storage.local.set({ urlCache });
    });
}

// Extract base URL for hierarchical organization
function extractBaseUrl(url) {
    try {
        const urlObj = new URL(url);
        return `${urlObj.protocol}//${urlObj.hostname}`;
    } catch (error) {
        console.error('Error extracting base URL:', error);
        return url;
    }
}

// Get URL hierarchy for a given URL
function getUrlHierarchy(url) {
    try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
        
        const hierarchy = [];
        let currentPath = `${urlObj.protocol}//${urlObj.hostname}`;
        hierarchy.push(currentPath);
        
        for (const part of pathParts) {
            currentPath += `/${part}`;
            hierarchy.push(currentPath);
        }
        
        return hierarchy;
    } catch (error) {
        console.error('Error getting URL hierarchy:', error);
        return [url];
    }
}



// Message handling for communication with popup and content scripts
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    switch (request.action) {
        case 'getCurrentTab':
            getCurrentTab().then(sendResponse);
            return true; // Keep message channel open for async response
            
        case 'getUrlHierarchy':
            sendResponse(getUrlHierarchy(request.url));
            break;
            
        case 'cacheUrl':
            cacheTabUrl(request.tabId, request.url);
            sendResponse({ success: true });
            break;
            
        default:
            console.log('Unknown action:', request.action);
    }
});

// Get current active tab
async function getCurrentTab() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return {
            id: tab.id,
            url: tab.url,
            title: tab.title,
            baseUrl: extractBaseUrl(tab.url),
            hierarchy: getUrlHierarchy(tab.url)
        };
    } catch (error) {
        console.error('Error getting current tab:', error);
        return null;
    }
}
