// Background service worker for StickyLink Extension

// Initialize extension on install
chrome.runtime.onInstalled.addListener(async () => {
    console.log('StickyLink Extension installed');

    // Initialize storage structure if not exists
    chrome.storage.local.get(['todos', 'notes', 'urlCache'], (result) => {
        if (!result.todos) {
            chrome.storage.local.set({ todos: [] });
        }
        if (!result.notes) {
            chrome.storage.local.set({ notes: [] });
        }
        if (!result.urlCache) {
            chrome.storage.local.set({ urlCache: {} });
        }
    });

    // Initialize side panel behavior
    try {
        if (chrome.sidePanel) {
            await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
            console.log('Side panel behavior initialized');
        }
    } catch (error) {
        console.log('Could not initialize side panel behavior:', error);
    }
});

// Side panel is managed by Chrome - no need to track state manually

// Handle extension icon click - check user preference for mode
chrome.action.onClicked.addListener(async (tab) => {
    // Get user preference for default mode
    const result = await chrome.storage.local.get(['settings']);
    const settings = result.settings || {};
    const defaultMode = settings.defaultMode || 'sidebar'; // Default to sidebar

    console.log('Extension clicked, defaultMode:', defaultMode, 'settings:', settings);

    switch (defaultMode) {
        case 'floating':
            console.log('Opening floating window');
            openFloatingWindow();
            break;
        case 'popup':
            console.log('Opening popup window');
            openPopupWindow();
            break;
        case 'sidebar':
        default:
            console.log('Opening sidebar');
            openSidePanel(tab.windowId);
            break;
    }
});

async function openSidePanel(windowId) {
    try {
        // Check if sidePanel API is available
        if (!chrome.sidePanel) {
            console.log('Side panel API not available, falling back to popup');
            openPopupWindow();
            return;
        }

        // Enable side panel for this window if not already enabled
        await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });

        // Open the side panel for the specific window
        await chrome.sidePanel.open({ windowId });
        console.log('Side panel opened successfully for window:', windowId);
    } catch (error) {
        console.log('Could not open side panel:', error.message);
        console.log('Error details:', error);

        // Try alternative approach - set the side panel to open on action click
        try {
            await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
            console.log('Side panel behavior set, user needs to click extension icon');
        } catch (behaviorError) {
            console.log('Could not set side panel behavior:', behaviorError);
            // Final fallback to popup
            openPopupWindow();
        }
    }
}

function openFloatingWindow() {
    chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 400,
        height: 600,
        focused: true
    });
}

function openPopupWindow() {
    chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 380,
        height: 580,
        focused: true
    });
}

// Window cleanup handled by Chrome automatically

// Track tab changes to cache URLs
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        cacheTabUrl(tabId, tab.url);
    }
});

// Track tab activation (switching between tabs)
chrome.tabs.onActivated.addListener((activeInfo) => {
    chrome.tabs.get(activeInfo.tabId, (tab) => {
        if (tab && tab.url) {
            // Tab changed - side panel will automatically update
            console.log(`Active tab changed to: ${tab.url}`);
        }
    });
});

// Track tab removal to maintain cache
chrome.tabs.onRemoved.addListener((tabId) => {
    // Keep URL in cache even after tab is closed
    // This allows todos/notes to persist for closed tabs
    console.log(`Tab ${tabId} closed, URL cached for future reference`);
});

// Cache tab URLs for persistence
function cacheTabUrl(tabId, url) {
    chrome.storage.local.get(['urlCache'], (result) => {
        const urlCache = result.urlCache || {};
        urlCache[tabId] = {
            url: url,
            timestamp: Date.now(),
            baseUrl: extractBaseUrl(url)
        };
        
        chrome.storage.local.set({ urlCache });
    });
}

// Extract base URL for hierarchical organization
function extractBaseUrl(url) {
    try {
        const urlObj = new URL(url);
        return `${urlObj.protocol}//${urlObj.hostname}`;
    } catch (error) {
        console.error('Error extracting base URL:', error);
        return url;
    }
}

// Get URL hierarchy for a given URL
function getUrlHierarchy(url) {
    try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
        
        const hierarchy = [];
        let currentPath = `${urlObj.protocol}//${urlObj.hostname}`;
        hierarchy.push(currentPath);
        
        for (const part of pathParts) {
            currentPath += `/${part}`;
            hierarchy.push(currentPath);
        }
        
        return hierarchy;
    } catch (error) {
        console.error('Error getting URL hierarchy:', error);
        return [url];
    }
}



// Message handling for communication with popup and content scripts
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    switch (request.action) {
        case 'getCurrentTab':
            getCurrentTab().then(sendResponse);
            return true; // Keep message channel open for async response
            
        case 'getUrlHierarchy':
            sendResponse(getUrlHierarchy(request.url));
            break;
            
        case 'cacheUrl':
            cacheTabUrl(request.tabId, request.url);
            sendResponse({ success: true });
            break;
            
        default:
            console.log('Unknown action:', request.action);
    }
});

// Get current active tab
async function getCurrentTab() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return {
            id: tab.id,
            url: tab.url,
            title: tab.title,
            baseUrl: extractBaseUrl(tab.url),
            hierarchy: getUrlHierarchy(tab.url)
        };
    } catch (error) {
        console.error('Error getting current tab:', error);
        return null;
    }
}
