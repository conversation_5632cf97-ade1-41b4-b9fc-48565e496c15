<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Notes Extension - Full Screen</title>
    <link rel="stylesheet" href="popup.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .fullscreen-header {
            background: #667eea;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .fullscreen-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .fullscreen-content {
            padding: 20px;
            min-height: calc(100vh - 80px);
        }
        
        .global-section {
            margin-bottom: 40px;
        }
        
        .global-section h2 {
            color: #374151;
            font-size: 20px;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }
        
        .global-form {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .global-form h3 {
            margin: 0 0 12px 0;
            color: #374151;
            font-size: 16px;
        }
        
        .form-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }
        
        .fullscreen .input-field,
        .fullscreen .textarea-field {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .fullscreen .textarea-field {
            min-height: 80px;
            resize: vertical;
        }
        
        .fullscreen .add-btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .fullscreen .add-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }
        
        .fullscreen .item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .fullscreen .item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="fullscreen">
    <div class="container">
        <div class="fullscreen-header">
            <h1>📝 Todo Notes Extension - Global Items</h1>
        </div>
        
        <div class="fullscreen-content">
            <!-- Global Todos Section -->
            <div class="global-section">
                <h2>✅ Global Todos</h2>
                
                <div class="global-form">
                    <h3>Add New Global Todo</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="global-todo-text">Todo Text</label>
                            <input type="text" id="global-todo-text" class="input-field" placeholder="Enter your todo...">
                        </div>
                        <div class="form-group">
                            <label for="global-todo-date">Due Date (Optional)</label>
                            <input type="date" id="global-todo-date" class="input-field">
                        </div>
                        <button id="add-global-todo-btn" class="add-btn">Add Todo</button>
                    </div>
                </div>
                
                <div id="global-todos-list" class="items-grid"></div>
            </div>
            
            <!-- Global Notes Section -->
            <div class="global-section">
                <h2>📝 Global Notes</h2>
                
                <div class="global-form">
                    <h3>Add New Global Note</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="global-note-title">Note Title (Optional)</label>
                            <input type="text" id="global-note-title" class="input-field" placeholder="Enter note title...">
                        </div>
                        <button id="add-global-note-btn" class="add-btn">Add Note</button>
                    </div>
                    <div class="form-group" style="margin-top: 12px;">
                        <label for="global-note-content">Note Content</label>
                        <textarea id="global-note-content" class="textarea-field" placeholder="Enter your note content..."></textarea>
                    </div>
                </div>
                
                <div id="global-notes-list" class="items-grid"></div>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
    <script>
        // Initialize fullscreen mode
        document.addEventListener('DOMContentLoaded', () => {
            if (window.app) {
                window.app.initFullscreenMode();
            }
        });
    </script>
</body>
</html>
