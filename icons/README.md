# Icons Directory

This directory should contain the following icon files for the Chrome extension:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows computers often require this size)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store and installation)

## Creating Icons

You can create simple icons using any image editor. For a todo/notes extension, consider:

- A checklist icon
- A notepad icon
- A combination of both
- Simple geometric shapes with todo/note symbolism

## Temporary Solution

For development purposes, you can create simple colored squares as placeholder icons:

```bash
# Create simple colored PNG files (requires ImageMagick)
convert -size 16x16 xc:blue icons/icon16.png
convert -size 32x32 xc:blue icons/icon32.png
convert -size 48x48 xc:blue icons/icon48.png
convert -size 128x128 xc:blue icons/icon128.png
```

Or use online icon generators to create proper icons.
