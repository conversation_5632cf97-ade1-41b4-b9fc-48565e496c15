# Todo Notes Chrome Extension

A powerful Chrome extension for managing todos and notes with hierarchical URL-based organization.

## Features

### Core Functionality
- **Global Todos & Notes**: Access your tasks and notes from anywhere
- **Tab-Specific**: Link todos/notes to specific URLs with caching for closed tabs
- **Base URL Hierarchy**: Organize tasks by domain with parent-child relationships
- **Flexible Grouping**: Group by URL, due date, or creation date

### Key Capabilities
- ✅ Create and manage todos with due dates
- 📝 Create and manage rich notes
- 🌐 Three scope levels: Global, Current Tab, Base URL
- 📊 Hierarchical organization (mydomain.com/ sees all mydomain.com/* items)
- 🔄 Persistent storage with Chrome's storage API
- ⚡ Quick overlay for in-page todo/note creation (Ctrl+Shift+T)
- 🎯 Smart URL caching for closed tabs

## Installation

### Development Installation
1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your toolbar

### Creating Icons (Optional)
1. Open `create_icons.html` in your browser
2. Click "Download Icons" to get placeholder icons
3. Save the downloaded icons in the `icons/` directory
4. Reload the extension in Chrome

## Usage

### Basic Usage
1. Click the extension icon in your toolbar to open the popup
2. Choose your scope:
   - **Global**: Tasks visible everywhere
   - **Current Tab**: Tasks linked to the current URL
   - **Base URL**: Tasks linked to the current domain
3. Switch between "Todos" and "Notes" tabs
4. Add your todos and notes!

### Advanced Features

#### URL Hierarchy
When using "Base URL" scope on `mydomain.com/`, you'll see:
- All todos/notes from `mydomain.com/`
- All todos/notes from `mydomain.com/dashboard`
- All todos/notes from `mydomain.com/data`
- etc.

Items are grouped by their specific URLs for easy organization.

#### Quick Overlay
- Press `Ctrl+Shift+T` on any webpage to open the quick overlay
- Add todos and notes without opening the popup
- View current page items instantly

#### Grouping Options
- **None**: Simple list view
- **URL**: Group by specific URLs (useful for Base URL scope)
- **Due Date**: Group todos by their due dates
- **Created Date**: Group by creation date

## File Structure

```
todonotesextension/
├── manifest.json          # Extension configuration
├── popup.html            # Main popup interface
├── popup.css             # Popup styling
├── popup.js              # Popup functionality
├── background.js         # Background service worker
├── content.js            # Content script for page integration
├── content.css           # Content script styling
├── icons/                # Extension icons
├── create_icons.html     # Icon generator utility
└── README.md            # This file
```

## Technical Details

### Architecture
- **Manifest V3**: Uses the latest Chrome extension format
- **Service Worker**: Background script for tab management and URL caching
- **Content Script**: Injected into all pages for overlay functionality
- **Chrome Storage API**: Persistent local storage for todos and notes

### Data Structure
```javascript
// Todo structure
{
  id: "unique-id",
  text: "Todo text",
  completed: false,
  dueDate: "2024-01-01" | null,
  createdAt: "2024-01-01T00:00:00.000Z",
  url: "https://example.com/page",
  baseUrl: "https://example.com",
  scope: "global" | "current-tab" | "base-url"
}

// Note structure
{
  id: "unique-id",
  title: "Note title",
  content: "Note content",
  createdAt: "2024-01-01T00:00:00.000Z",
  url: "https://example.com/page",
  baseUrl: "https://example.com",
  scope: "global" | "current-tab" | "base-url"
}
```

### Permissions
- `storage`: For persistent data storage
- `activeTab`: To get current tab information
- `tabs`: For tab management and URL tracking
- `scripting`: For content script injection
- `<all_urls>`: For content script access on all websites

## Development

### Next Steps
1. Add icons to the `icons/` directory
2. Test the extension on various websites
3. Add more advanced features like:
   - Todo priorities
   - Note categories
   - Export/import functionality
   - Sync across devices
   - Search functionality

### Testing
1. Load the extension in Chrome
2. Test on different websites
3. Try all three scope modes
4. Test the overlay functionality
5. Verify data persistence across browser sessions

## Contributing

Feel free to contribute by:
- Adding new features
- Improving the UI/UX
- Fixing bugs
- Adding tests
- Improving documentation

## License

This project is open source. Feel free to use and modify as needed.
