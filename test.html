<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Notes Extension Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .test-instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .test-instructions ol {
            margin: 15px 0;
        }
        
        .test-instructions li {
            margin-bottom: 8px;
        }
        
        .keyboard-shortcut {
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Todo Notes Extension Test Page</h1>
        <p>Use this page to test the Todo Notes Chrome Extension functionality</p>
    </div>
    
    <div class="section">
        <h2>📋 Extension Features to Test</h2>
        <ul>
            <li><strong>Modern Design:</strong> Check if the popup has a modern, Tabos-like design</li>
            <li><strong>Scope Switching:</strong> Test "Current Tab", "Website", and "All" scopes</li>
            <li><strong>URL-First Workflow:</strong> Create todos specific to this page</li>
            <li><strong>Smart Due Dates:</strong> Add todos with due dates (today/tomorrow/specific dates)</li>
            <li><strong>Completed Todos:</strong> Mark todos as complete and see them move to completed section</li>
            <li><strong>Round Checkboxes:</strong> Verify checkboxes are round and styled properly</li>
            <li><strong>URL Links:</strong> Click URL links to open pages in new tabs</li>
            <li><strong>Notes Functionality:</strong> Create and manage notes</li>
            <li><strong>Settings:</strong> Access settings modal and test layout options</li>
        </ul>
    </div>
    
    <div class="section">
        <div class="test-instructions">
            <h3>🧪 Testing Instructions</h3>
            <ol>
                <li>Click the Todo Notes extension icon in your Chrome toolbar</li>
                <li>The popup should open with a modern design and show "Current Tab" scope by default</li>
                <li>Add a todo for this page: "Test todo for test page"</li>
                <li>Set a due date for today and verify it shows "Today"</li>
                <li>Switch to "Website" scope and verify the todo still appears</li>
                <li>Switch to "All" scope and verify the todo appears there too</li>
                <li>Mark the todo as complete and verify it moves to the completed section</li>
                <li>Add a note: "Test note for this page"</li>
                <li>Click the URL link next to items to verify it opens this page in a new tab</li>
                <li>Test the quick overlay with <span class="keyboard-shortcut">Ctrl+Shift+T</span></li>
                <li>Open settings and test different layout options</li>
            </ol>
        </div>
    </div>
    
    <div class="section">
        <h2>🌐 Multi-Page Testing</h2>
        <p>To test the hierarchical URL functionality:</p>
        <ul>
            <li>Open this page: <code>file:///path/to/test.html</code></li>
            <li>Open another page on the same domain</li>
            <li>Create todos on both pages</li>
            <li>Switch between "Current Tab" and "Website" scopes to see filtering in action</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>✅ Expected Behavior</h2>
        <ul>
            <li><strong>Current Tab:</strong> Shows only todos/notes for the exact current URL</li>
            <li><strong>Website:</strong> Shows todos/notes for all pages on the same domain</li>
            <li><strong>All:</strong> Shows all todos/notes regardless of URL</li>
            <li><strong>Completed Todos:</strong> Automatically move to separate section with strikethrough</li>
            <li><strong>Due Dates:</strong> Display as "Today", "Tomorrow", or actual date</li>
            <li><strong>URL Links:</strong> Clickable links that open the associated page</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🐛 Common Issues to Check</h2>
        <ul>
            <li>Extension popup opens correctly</li>
            <li>Data persists after closing and reopening popup</li>
            <li>Scope switching works without losing data</li>
            <li>Completed todos section shows/hides properly</li>
            <li>Settings modal opens and closes correctly</li>
            <li>Keyboard shortcut (Ctrl+Shift+T) works for overlay</li>
            <li>No console errors in browser developer tools</li>
        </ul>
    </div>
</body>
</html>
