<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h1>Icon Generator for Todo Notes Extension</h1>
    <p>This page generates simple placeholder icons for the extension.</p>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid black; margin: 5px;"></canvas>
    <canvas id="canvas32" width="32" height="32" style="border: 1px solid black; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid black; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid black; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="downloadIcons()">Download Icons</button>
    
    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#4285f4';
            ctx.fillRect(0, 0, size, size);
            
            // White checkmark and note symbol
            ctx.fillStyle = 'white';
            ctx.font = `${Math.floor(size * 0.6)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (size <= 32) {
                ctx.fillText('✓', size/2, size/2);
            } else {
                // For larger icons, draw both checkmark and note
                ctx.font = `${Math.floor(size * 0.3)}px Arial`;
                ctx.fillText('✓', size/3, size/2);
                ctx.fillText('📝', 2*size/3, size/2);
            }
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadIcons() {
            downloadCanvas(document.getElementById('canvas16'), 'icon16.png');
            downloadCanvas(document.getElementById('canvas32'), 'icon32.png');
            downloadCanvas(document.getElementById('canvas48'), 'icon48.png');
            downloadCanvas(document.getElementById('canvas128'), 'icon128.png');
        }
        
        // Create icons on page load
        window.onload = function() {
            createIcon(document.getElementById('canvas16'), 16);
            createIcon(document.getElementById('canvas32'), 32);
            createIcon(document.getElementById('canvas48'), 48);
            createIcon(document.getElementById('canvas128'), 128);
        };
    </script>
</body>
</html>
