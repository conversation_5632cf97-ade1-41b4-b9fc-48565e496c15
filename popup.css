* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    color: #1a1a1a;
    background: #ffffff;
    line-height: 1.5;
}

.container {
    width: 500px;
    max-width: 800px;
    max-height: 900px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: auto;
    border: 1px solid #e5e7eb;
    resize: both;
    min-width: 350px;
    min-height: 500px;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    position: relative;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.header h1 {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.icon-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.current-url {
    font-size: 12px;
    opacity: 0.9;
    word-break: break-all;
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 10px;
    border-radius: 6px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.scope-selector {
    padding: 16px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.scope-tabs {
    display: flex;
    background: #ffffff;
    border-radius: 10px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
}

.scope-btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    color: #6b7280;
}

.scope-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.scope-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    transform: translateY(-1px);
}

.grouping-selector {
    margin-top: 12px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-completed-btn {
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: auto;
}

.view-completed-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.global-mode-toggle {
    padding: 8px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.global-mode-toggle label {
    color: white;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.global-mode-toggle input[type="checkbox"] {
    margin: 0;
}

.global-mode-hint {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
}

.global-mode-icon-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
    padding: 4px 8px;
}

.global-mode-icon {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.4;
    filter: grayscale(100%);
}

.global-mode-icon:hover {
    opacity: 0.7;
    transform: scale(1.1);
}

.global-mode-icon.active {
    opacity: 1;
    filter: grayscale(0%);
    background: rgba(102, 126, 234, 0.1);
}

.global-mode-label {
    color: #ffffff;
    font-size: 11px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    opacity: 0.8;
}

.grouping-selector label {
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.group-select {
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.group-select option {
    background: #667eea;
    color: white;
}

.content {
    max-height: 700px;
    overflow-y: auto;
    padding: 0;
    flex: 1;
}

.main-content {
    padding: 20px;
}

.section {
    margin-bottom: 32px;
}

.section:last-child {
    margin-bottom: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.section-header.collapsible:hover {
    background: #f8f9fa;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.collapse-btn {
    background: none;
    border: none;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.collapse-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.collapse-btn.collapsed {
    transform: rotate(-90deg);
}

.section-content {
    transition: all 0.3s ease;
    overflow: hidden;
}

.section-content.collapsed {
    max-height: 0;
    opacity: 0;
}

.toggle-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.add-item {
    margin-top: 16px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.add-item-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.add-item-form.single-line {
    flex-direction: row;
    align-items: center;
    gap: 8px;
}

.add-item-form.single-line input[type="text"] {
    flex: 1;
    margin: 0;
}

.add-item-form.single-line .global-mode-icon,
.add-item-form.single-line .icon-btn,
.add-item-form.single-line .cancel-btn {
    margin: 0;
    padding: 6px 8px;
    min-width: auto;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-item input,
.add-item textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
    background: white;
}

.add-item input:focus,
.add-item textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.add-item textarea {
    resize: vertical;
    min-height: 80px;
}

.add-item-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.date-input {
    flex: 0 0 auto;
    width: 140px;
}

.add-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    flex: 1;
}

.add-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.item.completed {
    opacity: 0.7;
    background: #f9fafb;
}

.item.completed .item-text {
    text-decoration: line-through;
    color: #9ca3af;
}

.item.temporary {
    border-color: #fbbf24;
    background: #fffbeb;
    opacity: 0.8;
}

.item.temporary::after {
    content: "Auto-saving...";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #f59e0b;
    background: #fef3c7;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.drag-handle {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    cursor: grab;
    font-size: 14px;
    line-height: 1;
    margin-right: 8px;
    flex-shrink: 0;
    transition: color 0.2s ease;
    user-select: none;
}

.drag-handle:hover {
    color: #667eea;
}

.drag-handle:active {
    cursor: grabbing;
    color: #4f46e5;
}

.item-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    margin-right: 12px;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.item-checkbox:checked {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
}

.item-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.item-content {
    flex: 1;
    min-width: 0;
}

.item-text {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.4;
    margin-bottom: 4px;
    word-wrap: break-word;
}

.item-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    margin-top: 6px;
}

.item-url-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
}

.item-url-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.favicon {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 2px;
}

.fallback-icon {
    font-size: 14px;
}

.item-due-date {
    background: #fef3c7;
    color: #92400e;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.item-due-date.today {
    background: #fee2e2;
    color: #dc2626;
}

.item-due-date.tomorrow {
    background: #dbeafe;
    color: #2563eb;
}

.item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.item:hover .item-actions {
    opacity: 1;
}

.item-actions button {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #6b7280;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.item-actions button:hover {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-1px);
}

.completed-section {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.completed-header {
    margin-bottom: 12px;
}

.completed-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #6b7280;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #6b7280;
}

.empty-state p {
    margin-bottom: 8px;
    font-size: 14px;
}

.empty-state .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.setting-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

.setting-description {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
}

/* New Item Buttons */
.new-item-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.new-item-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.cancel-btn {
    background: #f3f4f6;
    color: #6b7280;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

/* URL Truncation */
.url-link {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
}

.url-link:hover {
    max-width: none;
    white-space: normal;
    word-break: break-all;
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #1f2937;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    animation: slideUp 0.3s ease;
    min-width: 300px;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 8px 0;
    margin-bottom: 4px;
}

.toast-actions {
    display: flex;
    gap: 16px;
    align-items: center;
}

.undo-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.undo-btn:hover {
    background: #2563eb;
}

.undo-btn::before {
    content: '↩️';
    font-size: 14px;
}

.close-toast {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.toast-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 100%;
    transition: width 0.1s linear;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #1f2937;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1002;
    animation: slideDown 0.3s ease;
    min-width: 200px;
    display: none;
}

.notification-toast.success {
    background: #10b981;
}

.notification-toast.error {
    background: #ef4444;
}

.notification-toast.warning {
    background: #f59e0b;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Sidebar mode adjustments for notifications */
.sidebar-mode .notification-toast {
    position: absolute;
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    min-width: auto;
}

.sidebar-mode .toast {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    width: auto;
    min-width: auto;
}

.modal-content.small {
    max-width: 300px;
}

.quick-date-options {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.quick-date-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 80px;
}

.quick-date-btn:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.quick-date-btn:active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.date-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.note-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.textarea-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    margin-top: 8px;
}

/* Completed/Archived Page */
.page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 1000;
    overflow-y: auto;
}

.page-header {
    background: #667eea;
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.page-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.completed-archived-content {
    padding: 20px;
}

.item.completed .todo-text {
    text-decoration: line-through;
    opacity: 0.7;
}

.item.archived {
    opacity: 0.8;
}

.completed-date, .archived-date {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.restore-todo-btn, .restore-note-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.restore-todo-btn:hover, .restore-note-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Sidebar Mode Styling */
.sidebar-mode {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    min-width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-mode .container {
    min-height: 100vh;
    height: 100vh;
    width: 100%;
    border-radius: 0;
    box-shadow: none;
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 0;
    padding: 16px;
    box-sizing: border-box;
}

.sidebar-mode .header {
    border-radius: 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.sidebar-mode .content {
    padding: 16px;
}

.sidebar-mode .item {
    margin-bottom: 8px;
}

.sidebar-mode .item-text {
    font-size: 13px;
    line-height: 1.4;
}

.sidebar-mode .url-link {
    font-size: 12px;
}

.sidebar-mode .section-header h3 {
    font-size: 16px;
}

.sidebar-mode .add-btn {
    padding: 8px 16px;
    font-size: 13px;
}

.date-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* Empty state improvements */
.empty-state {
    padding: 20px;
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

.section:has(.items-list:empty) .empty-state {
    padding: 10px;
    font-size: 12px;
}

.created-date {
    font-size: 12px;
    color: #6b7280;
    font-weight: normal;
    float: right;
}

.group-container {
    margin-bottom: 16px;
}

.group-header {
    margin: 16px 0 8px 0;
    padding: 8px 12px;
    background: #f8fafc;
    border-left: 3px solid #667eea;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background 0.2s ease;
}

.group-header:hover {
    background: #f1f5f9;
}

.group-header.collapsible {
    cursor: pointer;
}

.group-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    flex: 1;
}

/* Scrollbar styling */
.content::-webkit-scrollbar {
    width: 6px;
}

.content::-webkit-scrollbar-track {
    background: #f9fafb;
}

.content::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Sidebar mode styling */
.sidebar-mode .container {
    height: 100vh;
    max-height: none;
    border-radius: 0;
    box-shadow: none;
}

.sidebar-mode .content {
    max-height: calc(100vh - 200px);
    flex: 1;
    overflow-y: auto;
}

/* Drag and Drop Styles */
.item[draggable="true"] {
    cursor: move;
}

.item[draggable="true"]:hover {
    background: rgba(102, 126, 234, 0.05);
}

.item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.drag-over {
    border-top: 2px solid #667eea;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .container {
        width: 100%;
        max-width: 400px;
        border-radius: 0;
    }

    .header {
        padding: 16px;
    }

    .main-content {
        padding: 16px;
    }
}
