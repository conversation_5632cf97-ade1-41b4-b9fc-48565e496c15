<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Notes</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-top">
                <h1>📝 Todo Notes</h1>
                <div class="header-actions">
                    <button id="fullscreen-btn" class="icon-btn" title="Open in Full Screen">🔳</button>
                    <button id="settings-btn" class="icon-btn" title="Settings">⚙️</button>
                    <button id="sidebar-btn" class="icon-btn" title="Open in Sidebar">▶️</button>
                </div>
            </div>
            <div class="url-info">
                <span id="current-url" class="current-url"></span>
            </div>
        </header>

        <div class="scope-selector">
            <div class="scope-tabs">
                <button class="scope-btn active" data-scope="all" title="Show todos/notes from all websites">All</button>
                <button class="scope-btn" data-scope="website" title="Show todos/notes from the current website only">Website</button>
                <button class="scope-btn" data-scope="current-tab" title="Show todos/notes from the current page only">Current Tab</button>
            </div>
            <div class="grouping-selector" id="grouping-selector" style="display: block;">
                <label for="group-by">Group by:</label>
                <select id="group-by" class="group-select" title="Organize items by category for better organization">
                    <option value="none">None</option>
                    <option value="website">Website</option>
                    <option value="due-date">Due Date</option>
                    <option value="created-date">Created Date</option>
                </select>
                <button id="view-completed-btn" class="view-completed-btn" title="View Completed & Archived">🗃️</button>
            </div>

        </div>
        
        <div class="content">
            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Todos Section -->
                <div class="section todos-section">
                    <div class="section-header collapsible" data-section="todos">
                        <h2>📋 Todos <span id="todos-count" class="count">(0)</span></h2>
                        <div class="section-actions">
                            <button id="new-todo-btn" class="new-item-btn" title="Add a new todo item">+ New To-Do</button>
                            <button class="collapse-btn" title="Collapse/expand this section">▼</button>
                        </div>
                    </div>

                    <div class="section-content" id="todos-content">
                        <div id="todos-list" class="items-list"></div>

                        <div class="add-item" id="add-todo-section" style="display: none;">
                            <div class="add-item-form">
                                <input type="text" id="todo-input" placeholder="Add a new todo for this page...">
                                <div class="global-mode-toggle-inline">
                                    <label title="When enabled, todos are not linked to any specific website and appear everywhere">
                                        <input type="checkbox" id="global-mode-toggle-todo"> Global Mode
                                    </label>
                                    <span class="global-mode-hint">Create todo not linked to any URL</span>
                                </div>
                                <div class="add-item-actions">
                                    <button id="calendar-btn" class="icon-btn" title="Set due date">📅</button>
                                    <input type="date" id="todo-due-date" class="date-input" style="display: none;">
                                    <button id="add-todo-btn" class="add-btn">Add Todo</button>
                                    <button id="cancel-todo-btn" class="cancel-btn">Cancel</button>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Notes Section -->
                <div class="section notes-section">
                    <div class="section-header collapsible" data-section="notes">
                        <h2>📝 Notes <span id="notes-count" class="count">(0)</span></h2>
                        <div class="section-actions">
                            <button id="new-note-btn" class="new-item-btn" title="Add a new note">+ New Note</button>
                            <button class="collapse-btn" title="Collapse/expand this section">▼</button>
                        </div>
                    </div>

                    <div class="section-content" id="notes-content">
                        <div id="notes-list" class="items-list"></div>

                        <div class="add-item" id="add-note-section" style="display: none;">
                            <div class="add-item-form">
                                <input type="text" id="note-title" placeholder="Note title (optional)...">
                                <textarea id="note-content" placeholder="Add a new note for this page..."></textarea>
                                <div class="global-mode-toggle-inline">
                                    <label title="When enabled, notes are not linked to any specific website and appear everywhere">
                                        <input type="checkbox" id="global-mode-toggle-note"> Global Mode
                                    </label>
                                    <span class="global-mode-hint">Create note not linked to any URL</span>
                                </div>
                                <div class="add-item-actions">
                                    <button id="add-note-btn" class="add-btn">Add Note</button>
                                    <button id="cancel-note-btn" class="cancel-btn">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed/Archived Page -->
        <div id="completed-archived-page" class="page" style="display: none;">
            <div class="page-header">
                <button id="back-to-main-btn" class="back-btn">← Back</button>
                <h2>Completed & Archived</h2>
            </div>

            <div class="completed-archived-content">
                <div class="section">
                    <div class="section-header">
                        <h3>✅ Completed Todos <span id="completed-page-count" class="count">(0)</span></h3>
                    </div>
                    <div class="section-content">
                        <div id="completed-page-list" class="items-list"></div>
                    </div>
                </div>

                <div class="section">
                    <div class="section-header">
                        <h3>📁 Archived Notes <span id="archived-page-count" class="count">(0)</span></h3>
                    </div>
                    <div class="section-content">
                        <div id="archived-page-list" class="items-list"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button id="close-settings" class="close-btn">×</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="sidebar-mode"> Use Sidebar Mode as Default
                        </label>
                        <p class="setting-description">When enabled, the extension will automatically open in sidebar mode for better persistence across tabs.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Picker Modal -->
        <div id="date-picker-modal" class="modal" style="display: none;">
            <div class="modal-content small">
                <div class="modal-header">
                    <h3>Select Due Date</h3>
                    <button class="close-btn" id="close-date-picker">×</button>
                </div>
                <div class="modal-body">
                    <input type="date" id="date-picker-input" class="date-input">
                    <div class="date-actions">
                        <button id="set-date-btn" class="add-btn">Set Date</button>
                        <button id="clear-date-btn" class="cancel-btn">Clear Date</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Note Modal -->
        <div id="edit-note-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Note</h3>
                    <button class="close-btn" id="close-edit-note">×</button>
                </div>
                <div class="modal-body">
                    <input type="text" id="edit-note-title" class="input-field" placeholder="Note title">
                    <textarea id="edit-note-content" class="textarea-field" placeholder="Note content" rows="6"></textarea>
                    <div class="note-actions">
                        <button id="save-note-btn" class="add-btn">Save Note</button>
                        <button id="cancel-edit-note-btn" class="cancel-btn">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Undo Delete Toast -->
        <div id="undo-toast" class="toast" style="display: none;">
            <div class="toast-content">
                <span id="undo-message">Item deleted</span>
                <div class="toast-actions">
                    <button id="undo-btn" class="undo-btn">Undo</button>
                    <button id="close-toast" class="close-toast">×</button>
                </div>
            </div>
            <div class="toast-progress">
                <div id="toast-progress-bar" class="toast-progress-bar"></div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
