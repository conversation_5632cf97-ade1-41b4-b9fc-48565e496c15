<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Notes</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-top">
                <h1>📝 Todo Notes</h1>
                <div class="header-actions">
                    <button id="settings-btn" class="icon-btn" title="Settings">⚙️</button>
                    <button id="sidebar-btn" class="icon-btn" title="Open in Sidebar">📋</button>
                </div>
            </div>
            <div class="url-info">
                <span id="current-url" class="current-url"></span>
            </div>
        </header>

        <div class="scope-selector">
            <div class="scope-tabs">
                <button class="scope-btn active" data-scope="current-tab">Current Tab</button>
                <button class="scope-btn" data-scope="website">Website</button>
                <button class="scope-btn" data-scope="all">All</button>
            </div>
        </div>
        
        <div class="content">
            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Todos Section -->
                <div class="section todos-section">
                    <div class="section-header">
                        <h2>📋 Todos</h2>
                        <div class="section-actions">
                            <button id="toggle-completed" class="toggle-btn">Hide Completed</button>
                        </div>
                    </div>

                    <div id="todos-list" class="items-list"></div>

                    <div class="add-item">
                        <div class="add-item-form">
                            <input type="text" id="todo-input" placeholder="Add a new todo for this page...">
                            <div class="add-item-actions">
                                <input type="date" id="todo-due-date" class="date-input">
                                <button id="add-todo-btn" class="add-btn">Add Todo</button>
                            </div>
                        </div>
                    </div>

                    <div id="completed-todos" class="completed-section">
                        <div class="completed-header">
                            <h3>✅ Completed</h3>
                        </div>
                        <div id="completed-todos-list" class="items-list"></div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="section notes-section">
                    <div class="section-header">
                        <h2>📝 Notes</h2>
                    </div>

                    <div id="notes-list" class="items-list"></div>

                    <div class="add-item">
                        <div class="add-item-form">
                            <input type="text" id="note-title" placeholder="Note title (optional)...">
                            <textarea id="note-content" placeholder="Add a new note for this page..."></textarea>
                            <div class="add-item-actions">
                                <button id="add-note-btn" class="add-btn">Add Note</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button id="close-settings" class="close-btn">×</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label>Layout Style:</label>
                        <select id="layout-setting">
                            <option value="current-tab">Current Tab Format</option>
                            <option value="todos-top">Todos on Top</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="sidebar-mode"> Open in Sidebar Mode
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
