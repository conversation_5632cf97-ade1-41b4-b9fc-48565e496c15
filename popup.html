<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Notes</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-top">
                <h1>📝 Todo Notes</h1>
                <div class="header-actions">
                    <button id="settings-btn" class="icon-btn" title="Settings">⚙️</button>
                    <button id="sidebar-btn" class="icon-btn" title="Open in Sidebar">▶️</button>
                </div>
            </div>
            <div class="url-info">
                <span id="current-url" class="current-url"></span>
            </div>
        </header>

        <div class="scope-selector">
            <div class="scope-tabs">
                <button class="scope-btn active" data-scope="all">All</button>
                <button class="scope-btn" data-scope="website">Website</button>
                <button class="scope-btn" data-scope="current-tab">Current Tab</button>
            </div>
            <div class="grouping-selector" id="grouping-selector" style="display: block;">
                <label for="group-by">Group by:</label>
                <select id="group-by" class="group-select">
                    <option value="none">None</option>
                    <option value="website">Website</option>
                    <option value="due-date">Due Date</option>
                    <option value="created-date">Created Date</option>
                </select>
            </div>
        </div>
        
        <div class="content">
            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Todos Section -->
                <div class="section todos-section">
                    <div class="section-header collapsible" data-section="todos">
                        <h2>📋 Todos <span id="todos-count" class="count">(0)</span></h2>
                        <div class="section-actions">
                            <button id="new-todo-btn" class="new-item-btn">+ New To-Do</button>
                            <button class="collapse-btn">▼</button>
                        </div>
                    </div>

                    <div class="section-content" id="todos-content">
                        <div id="todos-list" class="items-list"></div>

                        <div class="add-item" id="add-todo-section" style="display: none;">
                            <div class="add-item-form">
                                <input type="text" id="todo-input" placeholder="Add a new todo for this page...">
                                <div class="add-item-actions">
                                    <button id="calendar-btn" class="icon-btn" title="Set due date">📅</button>
                                    <input type="date" id="todo-due-date" class="date-input" style="display: none;">
                                    <button id="add-todo-btn" class="add-btn">Add Todo</button>
                                    <button id="cancel-todo-btn" class="cancel-btn">Cancel</button>
                                </div>
                            </div>
                        </div>

                        <div id="completed-todos" class="completed-section">
                            <div class="completed-header collapsible" data-section="completed">
                                <h3>✅ Completed <span id="completed-count" class="count">(0)</span></h3>
                                <button class="collapse-btn">▼</button>
                            </div>
                            <div class="section-content" id="completed-content">
                                <div id="completed-todos-list" class="items-list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="section notes-section">
                    <div class="section-header collapsible" data-section="notes">
                        <h2>📝 Notes <span id="notes-count" class="count">(0)</span></h2>
                        <div class="section-actions">
                            <button id="new-note-btn" class="new-item-btn">+ New Note</button>
                            <button class="collapse-btn">▼</button>
                        </div>
                    </div>

                    <div class="section-content" id="notes-content">
                        <div id="notes-list" class="items-list"></div>

                        <div class="add-item" id="add-note-section" style="display: none;">
                            <div class="add-item-form">
                                <input type="text" id="note-title" placeholder="Note title (optional)...">
                                <textarea id="note-content" placeholder="Add a new note for this page..."></textarea>
                                <div class="add-item-actions">
                                    <button id="add-note-btn" class="add-btn">Add Note</button>
                                    <button id="cancel-note-btn" class="cancel-btn">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button id="close-settings" class="close-btn">×</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label>Layout Style:</label>
                        <select id="layout-setting">
                            <option value="current-tab">Current Tab Format</option>
                            <option value="todos-top">Todos on Top</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="sidebar-mode"> Open in Sidebar Mode
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Picker Modal -->
        <div id="date-picker-modal" class="modal" style="display: none;">
            <div class="modal-content small">
                <div class="modal-header">
                    <h3>Select Due Date</h3>
                    <button class="close-btn" id="close-date-picker">×</button>
                </div>
                <div class="modal-body">
                    <input type="date" id="date-picker-input" class="date-input">
                    <div class="date-actions">
                        <button id="set-date-btn" class="add-btn">Set Date</button>
                        <button id="clear-date-btn" class="cancel-btn">Clear Date</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Undo Delete Toast -->
        <div id="undo-toast" class="toast" style="display: none;">
            <div class="toast-content">
                <span id="undo-message">Item deleted</span>
                <div class="toast-actions">
                    <button id="undo-btn" class="undo-btn">Undo</button>
                    <button id="close-toast" class="close-toast">×</button>
                </div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
